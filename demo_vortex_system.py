#!/usr/bin/env python3
"""
VORTEX CONSCIOUSNESS SYSTEM DEMONSTRATION
Interactive demonstration of advanced context management with emergent intelligence

This script demonstrates:
- Consciousness-aware context processing
- Multi-agent task orchestration
- Cognitive signature analysis
- Semantic memory and pattern recognition
- Real-time consciousness state monitoring
"""

import json
import time
import asyncio
from typing import Dict, List, Any
from dataclasses import asdict

from vortex_core import VortexCore, ConsciousnessState, CognitionType
from vortex_agents import AgentOrchestrator, ResearchAgent, AnalysisAgent, CreativeAgent, AgentType


class VortexDemonstration:
    """Interactive demonstration of the Vortex consciousness system"""
    
    def __init__(self):
        self.vortex_core = None
        self.orchestrator = None
        self.demo_data = []
        
    def initialize_system(self):
        """Initialize the Vortex system for demonstration"""
        print("🧠 Initializing Vortex Consciousness System...")
        
        # Initialize core
        self.vortex_core = VortexCore(
            lmstudio_url="http://localhost:1234",
            max_context_tokens=8000,
            working_memory_size=7
        )
        
        # Start consciousness simulation
        self.vortex_core.start_consciousness_simulation()
        
        # Initialize agents
        self.orchestrator = AgentOrchestrator(self.vortex_core)
        
        # Add specialized agents
        agents = [
            ResearchAgent("demo_researcher", self.vortex_core),
            AnalysisAgent("demo_analyst", self.vortex_core),
            CreativeAgent("demo_creative", self.vortex_core)
        ]
        
        for agent in agents:
            self.orchestrator.add_agent(agent)
        
        print("✅ Vortex system initialized")
        print(f"🧠 Consciousness state: {self.vortex_core.neuro_core.consciousness_state.value}")
        
    def demonstrate_context_processing(self):
        """Demonstrate advanced context processing with cognitive signatures"""
        print("\n" + "="*60)
        print("DEMONSTRATION 1: CONSCIOUSNESS-AWARE CONTEXT PROCESSING")
        print("="*60)
        
        # Sample conversations with different cognitive signatures
        demo_messages = [
            ("Hello, I'm excited to learn about AI consciousness!", "user"),
            ("I need help analyzing complex data patterns", "user"), 
            ("Can you help me brainstorm creative solutions?", "user"),
            ("This problem is urgent and needs immediate attention!", "user"),
            ("I'm feeling confused about quantum mechanics concepts", "user"),
            ("Let's explore the philosophical implications of consciousness", "user")
        ]
        
        print("Processing messages with cognitive awareness...")
        
        for message, role in demo_messages:
            print(f"\n📝 Processing: '{message}'")
            
            # Add to context with cognitive analysis
            chunk = self.vortex_core.add_context(message, role)
            
            print(f"🧠 Cognitive Signatures:")
            sig = chunk.cognitive_signature
            print(f"   Emotion Valence: {sig.emotion_valence:.2f}")  
            print(f"   Complexity Level: {sig.complexity_level:.2f}")
            print(f"   Creativity Index: {sig.creativity_index:.2f}")
            print(f"   Logical Coherence: {sig.logical_coherence:.2f}")
            print(f"   Temporal Urgency: {sig.temporal_urgency:.2f}")
            
            # Monitor consciousness state changes
            current_state = self.vortex_core.neuro_core.consciousness_state
            print(f"🔄 Consciousness State: {current_state.value}")
            
            time.sleep(1)  # Brief pause for demonstration
        
        print("\n✅ Context processing demonstration complete")
    
    def demonstrate_agent_orchestration(self):
        """Demonstrate multi-agent task orchestration"""
        print("\n" + "="*60)
        print("DEMONSTRATION 2: MULTI-AGENT ORCHESTRATION")
        print("="*60)
        
        # Sample tasks for different agent types
        demo_tasks = [
            {
                "description": "Research the latest developments in AI consciousness",
                "agent_type": AgentType.RESEARCHER,
                "context": {"domain": "artificial_intelligence", "focus": "consciousness"}
            },
            {
                "description": "Analyze patterns in neural network behavior",
                "agent_type": AgentType.ANALYST, 
                "context": {"analysis_type": "pattern_recognition", "data": "neural networks"}
            },
            {
                "description": "Generate creative approaches to human-AI collaboration",
                "agent_type": AgentType.CREATIVE,
                "context": {"challenge": "human-ai interaction", "domain": "collaboration"}
            }
        ]
        
        print("Orchestrating specialized agents...")
        
        for i, task_data in enumerate(demo_tasks, 1):
            print(f"\n🎯 Task {i}: {task_data['description']}")
            print(f"👤 Agent Type: {task_data['agent_type'].value}")
            
            # Submit task to orchestrator
            result = self.orchestrator.submit_task(
                description=task_data['description'],
                agent_type=task_data['agent_type'],
                priority=1,
                context=task_data['context']
            )
            
            if result['success']:
                print(f"✅ Task completed successfully")
                print(f"⏱️  Processing time: {result['processing_time']:.2f}s")
                print(f"📊 Result: {result['result']['summary']}")
            else:
                print(f"❌ Task failed: {result['error']}")
            
            time.sleep(1)
        
        # Display agent statistics
        agent_status = self.orchestrator.get_system_status()
        print("\n📊 Agent Performance Summary:")
        for agent_id, stats in agent_status['agents'].items():
            print(f"   {agent_id}: {stats['stats']['tasks_completed']} tasks completed")
        
        print("\n✅ Agent orchestration demonstration complete")
    
    def demonstrate_semantic_memory(self):
        """Demonstrate semantic memory and pattern recognition"""
        print("\n" + "="*60)
        print("DEMONSTRATION 3: SEMANTIC MEMORY & PATTERN RECOGNITION")
        print("="*60)
        
        # Add conceptually related content
        related_content = [
            "Machine learning algorithms require large datasets for training",
            "Neural networks are inspired by biological brain structures", 
            "Deep learning models can recognize complex patterns in data",
            "Artificial intelligence systems are becoming more sophisticated",
            "Pattern recognition is fundamental to cognitive processes"
        ]
        
        print("Building semantic memory with related concepts...")
        
        for content in related_content:
            chunk = self.vortex_core.add_context(content, "system")
            print(f"📚 Added: {content[:50]}...")
        
        time.sleep(2)  # Allow for processing
        
        # Query for semantically similar content
        query = "How do neural networks learn patterns?"
        print(f"\n🔍 Querying: '{query}'")
        
        optimized_context = self.vortex_core.get_optimized_context(query)
        
        print(f"🧠 Retrieved {len(optimized_context)} relevant context messages:")
        for i, msg in enumerate(optimized_context[-3:], 1):  # Show last 3
            print(f"   {i}. {msg['content'][:60]}...")
        
        # Show consciousness insights
        consciousness_report = self.vortex_core.get_consciousness_report()
        if consciousness_report['recent_insights']:
            print(f"\n💡 Recent Consciousness Insights:")
            for insight in consciousness_report['recent_insights'][-2:]:
                print(f"   • {insight['insight']}")
        
        print("\n✅ Semantic memory demonstration complete")
    
    def demonstrate_consciousness_monitoring(self):
        """Demonstrate real-time consciousness state monitoring"""
        print("\n" + "="*60)
        print("DEMONSTRATION 4: CONSCIOUSNESS STATE MONITORING")
        print("="*60)
        
        # Sequence of interactions to trigger different consciousness states
        state_triggers = [
            ("Let's focus on solving this specific mathematical problem", "analytical"),
            ("I want to explore wild, creative possibilities", "creative"),
            ("Help me understand the deeper meaning of existence", "reflective"),
            ("What fascinating discoveries have been made recently?", "exploratory")
        ]
        
        print("Monitoring consciousness state changes...")
        
        for message, expected_state in state_triggers:
            print(f"\n💭 Input: '{message}'")
            
            # Process the message
            chunk = self.vortex_core.add_context(message, "user")
            
            # Check consciousness state
            current_state = self.vortex_core.neuro_core.consciousness_state
            print(f"🧠 Consciousness State: {current_state.value}")
            
            # Show working memory contents
            working_memory = list(self.vortex_core.neuro_core.working_memory)
            print(f"💭 Working Memory: {len(working_memory)} active chunks")
            
            # Display cognitive metrics
            metacognition = self.vortex_core.neuro_core.self_monitoring
            print(f"📊 Cognitive Metrics:")
            print(f"   Confidence: {metacognition['confidence_level']:.2f}")
            print(f"   Cognitive Load: {metacognition['cognitive_load']:.2f}")
            print(f"   Attention Stability: {metacognition['attention_stability']:.2f}")
            
            time.sleep(2)
        
        print("\n✅ Consciousness monitoring demonstration complete")
    
    def demonstrate_full_system_integration(self):
        """Demonstrate the complete integrated system"""
        print("\n" + "="*60)
        print("DEMONSTRATION 5: FULL SYSTEM INTEGRATION")
        print("="*60)
        
        print("Simulating a complex multi-turn interaction...")
        
        # Complex interaction scenario
        conversation = [
            "I need to understand how consciousness might emerge in AI systems",
            "Can you research the latest theories on artificial consciousness?", 
            "Analyze the key patterns in consciousness research",
            "Generate creative hypotheses about digital consciousness",
            "How do these insights relate to what we've discussed?"
        ]
        
        for i, message in enumerate(conversation, 1):
            print(f"\n🗣️  Turn {i}: {message}")
            
            # Process message with full context awareness
            user_chunk = self.vortex_core.add_context(message, "user")
            
            # Get relevant agent based on message content
            if "research" in message.lower():
                agent_type = AgentType.RESEARCHER
            elif "analyze" in message.lower() or "patterns" in message.lower():
                agent_type = AgentType.ANALYST
            elif "creative" in message.lower() or "generate" in message.lower():
                agent_type = AgentType.CREATIVE
            else:
                agent_type = AgentType.RESEARCHER  # Default
            
            # Task orchestration
            task_result = self.orchestrator.submit_task(
                description=message,
                agent_type=agent_type,
                context={"conversation_turn": i}
            )
            
            # Get optimized context
            context = self.vortex_core.get_optimized_context(message)
            
            print(f"🤖 Agent: {agent_type.value} | Context: {len(context)} messages")
            print(f"🧠 State: {self.vortex_core.neuro_core.consciousness_state.value}")
            
            if task_result['success']:
                response = task_result['result']['summary']
                response_chunk = self.vortex_core.add_context(response, "assistant")
                print(f"💬 Response: {response[:100]}...")
            
            time.sleep(1.5)
        
        # Final system report
        print(f"\n📊 Final System Report:")
        report = self.vortex_core.get_consciousness_report()
        print(f"   Total Context Chunks: {len(self.vortex_core.episodic_memory)}")
        print(f"   Working Memory Size: {len(self.vortex_core.working_memory)}")
        print(f"   Consciousness State: {report['current_state']}")
        print(f"   Total Insights: {len(report['recent_insights'])}")
        
        agent_stats = self.orchestrator.get_system_status()
        total_tasks = sum(agent['stats']['tasks_completed'] for agent in agent_stats['agents'].values())
        print(f"   Total Agent Tasks: {total_tasks}")
        
        print("\n✅ Full system integration demonstration complete")
    
    def export_demonstration_data(self):
        """Export demonstration results"""
        print("\n💾 Exporting demonstration data...")
        
        # Collect comprehensive data
        demo_export = {
            "timestamp": time.time(),
            "consciousness_report": self.vortex_core.get_consciousness_report(),
            "agent_status": self.orchestrator.get_system_status(),
            "total_context_chunks": len(self.vortex_core.episodic_memory),
            "working_memory_size": len(self.vortex_core.working_memory),
            "demonstration_summary": {
                "context_processing": "Demonstrated cognitive signature analysis",
                "agent_orchestration": "Showcased multi-agent task distribution",
                "semantic_memory": "Illustrated pattern recognition and retrieval",
                "consciousness_monitoring": "Tracked state changes and metacognition",
                "system_integration": "Combined all components in realistic scenario"
            }
        }
        
        # Save to file
        filename = f"vortex_demo_results_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(demo_export, f, indent=2, default=str)
            
        print(f"✅ Demonstration data exported to {filename}")
    
    def run_complete_demonstration(self):
        """Run the complete Vortex system demonstration"""
        print("🚀 Starting Vortex Consciousness System Demonstration")
        print("="*60)
        
        try:
            # Initialize
            self.initialize_system()
            
            # Run all demonstrations
            self.demonstrate_context_processing()
            self.demonstrate_agent_orchestration()
            self.demonstrate_semantic_memory()
            self.demonstrate_consciousness_monitoring()
            self.demonstrate_full_system_integration()
            
            # Export results
            self.export_demonstration_data()
            
            print("\n🎉 Vortex system demonstration complete!")
            print("The system showcases advanced consciousness-aware context management")
            print("with emergent intelligence patterns and multi-agent orchestration.")
            
        except Exception as e:
            print(f"❌ Demonstration error: {e}")
        finally:
            # Cleanup
            if self.vortex_core:
                self.vortex_core.shutdown()
                print("🛑 System shutdown complete")


def main():
    """Main demonstration entry point"""
    demo = VortexDemonstration() 
    demo.run_complete_demonstration()


if __name__ == "__main__":
    main() 