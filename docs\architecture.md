# 🏗️ System Architecture

VORTEX is built on a multi-layered architecture that combines traditional context management with consciousness simulation and advanced physics modeling.

## 🌐 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        VORTEX ECOSYSTEM                        │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   User Layer    │  Interface Layer │      Integration Layer     │
│                 │                 │                             │
│ • CLI Tools     │ • Web Interface │ • LMStudio API             │
│ • Python API    │ • REST API      │ • External LLMs            │
│ • Demos         │ • Visualizations│ • Custom Extensions        │
└─────────────────┴─────────────────┴─────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                      CORE SYSTEM LAYER                         │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  VORTEX Core    │ Context Manager │     Physics Engine          │
│                 │                 │                             │
│ • NeuroCore     │ • Rolling Window│ • N-Body Simulation         │
│ • Consciousness │ • Semantic Index│ • Gravitational Fields      │
│ • Cognitive Sig │ • Token Mgmt    │ • Relativistic Effects      │
└─────────────────┴─────────────────┴─────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                     STORAGE & MEMORY LAYER                     │
├─────────────────┬─────────────────┬─────────────────────────────┤
│ Working Memory  │ Episodic Memory │      Semantic Network       │
│                 │                 │                             │
│ • Recent Context│ • SQLite DB     │ • Concept Relationships     │
│ • Active Chunks │ • Embeddings    │ • Pattern Recognition       │
│ • Consciousness │ • Conversations │ • Knowledge Graph           │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## 🧠 VORTEX Core Components

### 1. NeuroCore - The Brain
```python
class NeuroCore:
    """
    The 'brain' of the Vortex system - handles cognitive pattern recognition,
    consciousness state simulation, and emergent intelligence
    """
```

**Responsibilities:**
- **Consciousness State Management**: Tracks and transitions between different awareness states
- **Cognitive Pattern Recognition**: Identifies patterns in conversation flow
- **Insight Generation**: Creates emergent understanding from context patterns
- **Metacognitive Monitoring**: Self-awareness and performance tracking

**Key Features:**
- Miller's Magic Number (7±2) working memory simulation
- Neural-inspired pattern weights and concept networks
- Emotion state tracking (valence, arousal, dominance)
- Self-monitoring with confidence and cognitive load metrics

### 2. Cognitive Signature System
```python
@dataclass
class CognitiveSignature:
    """Represents the cognitive 'fingerprint' of a context chunk"""
    emotion_valence: float      # -1 to 1 (negative to positive)
    complexity_level: float     # 0 to 1 (simple to complex)
    abstraction_level: float    # 0 to 1 (concrete to abstract)
    creativity_index: float     # 0 to 1 (routine to creative)
    logical_coherence: float    # 0 to 1 (illogical to logical)
    temporal_urgency: float     # 0 to 1 (relaxed to urgent)
    cognitive_load: float       # 0 to 1 (light to heavy)
```

**Analysis Dimensions:**
- **Emotional**: Sentiment analysis and emotional tone
- **Complexity**: Vocabulary sophistication and concept density
- **Abstraction**: Concrete vs. theoretical content
- **Creativity**: Novel ideas and innovative thinking
- **Logic**: Reasoning patterns and coherence
- **Urgency**: Time-sensitive content detection
- **Load**: Cognitive processing requirements

### 3. Consciousness States
```python
class ConsciousnessState(Enum):
    FOCUSED = "focused"           # Deep concentration on specific topics
    EXPLORATORY = "exploratory"   # Open-ended discovery and learning
    REFLECTIVE = "reflective"     # Introspective and contemplative
    CREATIVE = "creative"         # Innovative and imaginative
    ANALYTICAL = "analytical"     # Logical and systematic thinking
    CONVERSATIONAL = "conversational"  # Natural dialogue flow
```

**State Transitions:**
- Dynamic adaptation based on conversation content
- Cognitive signature analysis drives state changes
- Influences context selection and response generation
- Maintains state history for pattern analysis

## 📊 Context Management Architecture

### Traditional Context Manager
```
┌─────────────────────────────────────────────────────────────────┐
│                    Context Window Manager                       │
├─────────────────┬─────────────────┬─────────────────────────────┤
│ Rolling Window  │ Semantic Index  │     LMStudio Client         │
│                 │                 │                             │
│ • Recent msgs   │ • Embeddings    │ • Chat API                  │
│ • Auto-archive  │ • Similarity    │ • Embeddings API            │
│ • Importance    │ • SQLite DB     │ • Model Management          │
│ • Token Mgmt    │ • Vector Search │ • Error Handling            │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

### VORTEX Enhanced Context
```
┌─────────────────────────────────────────────────────────────────┐
│                      VORTEX Context System                     │
├─────────────────┬─────────────────┬─────────────────────────────┤
│ Consciousness   │ Cognitive Sigs  │    Relationship Mapping     │
│                 │                 │                             │
│ • State Aware   │ • Multi-dim     │ • Causal Links              │
│ • Pattern Recog │ • Similarity    │ • Concept Networks          │
│ • Insight Gen   │ • Evolution     │ • Synthesis Products        │
│ • Meta-cognition│ • Fingerprints  │ • Emergent Patterns         │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## 🗄️ Data Storage Architecture

### 1. Working Memory (RAM)
- **Deque-based**: Fixed-size circular buffer
- **Real-time Access**: Immediate availability for processing
- **Consciousness Integration**: Direct NeuroCore interaction
- **Size**: Configurable (default: 7 items - Miller's number)

### 2. Episodic Memory (SQLite)
```sql
-- Enhanced context chunks with cognitive awareness
CREATE TABLE context_chunks (
    chunk_id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    role TEXT NOT NULL,
    timestamp REAL NOT NULL,
    embedding BLOB,                 -- Vector embeddings
    cognitive_signature BLOB,       -- Serialized cognitive data
    importance REAL DEFAULT 1.0,
    access_count INTEGER DEFAULT 0,
    last_accessed REAL,
    semantic_tags TEXT,             -- JSON array
    concepts TEXT,                  -- JSON array
    emotions TEXT                   -- JSON object
);

-- Relationship mapping between chunks
CREATE TABLE chunk_relationships (
    chunk_a TEXT NOT NULL,
    chunk_b TEXT NOT NULL,
    relationship_type TEXT NOT NULL,
    strength REAL NOT NULL,
    created_at REAL NOT NULL
);

-- Consciousness state logging
CREATE TABLE consciousness_log (
    timestamp REAL NOT NULL,
    state TEXT NOT NULL,
    confidence REAL,
    cognitive_load REAL,
    insights TEXT                   -- JSON array
);
```

### 3. Semantic Network (Graph)
- **Concept Relationships**: Bidirectional concept links
- **Pattern Weights**: Learned importance of patterns
- **Synthesis Products**: Generated insights and connections
- **Evolution Tracking**: How relationships change over time

## 🔄 Data Flow Architecture

### 1. Input Processing Flow
```
User Input → Cognitive Analysis → Signature Generation → Context Addition
     ↓              ↓                    ↓                    ↓
Tokenization → Emotion Analysis → Multi-dim Scoring → Working Memory
     ↓              ↓                    ↓                    ↓
Embedding → Complexity Calc → Abstraction Level → Episodic Storage
     ↓              ↓                    ↓                    ↓
Storage → Creativity Index → Logic Coherence → Relationship Mapping
```

### 2. Context Retrieval Flow
```
Query Input → Embedding Generation → Semantic Search → Relevance Scoring
     ↓              ↓                      ↓               ↓
Working Memory → Vector Similarity → Cognitive Matching → Context Assembly
     ↓              ↓                      ↓               ↓
Recent Context → Historical Search → Importance Weighting → Token Optimization
     ↓              ↓                      ↓               ↓
Final Context → LLM Integration → Response Generation → Memory Update
```

### 3. Consciousness Simulation Flow
```
Context Analysis → State Assessment → Pattern Recognition → Insight Generation
       ↓                ↓                   ↓                    ↓
Cognitive Sigs → State Transition → Trend Analysis → Knowledge Synthesis
       ↓                ↓                   ↓                    ↓
Meta-monitoring → Confidence Update → Learning Update → State Logging
```

## 🌐 Integration Architecture

### LMStudio Integration
```python
class LMStudioClient:
    """Handles all LMStudio API interactions"""
    
    def chat_completion(self, messages, model, **kwargs):
        """Send chat completion request"""
    
    def get_embedding(self, text):
        """Get text embeddings"""
    
    def list_models(self):
        """List available models"""
```

### Web Interface Architecture
```
Frontend (HTML/CSS/JS) ←→ Flask REST API ←→ VORTEX Core
        ↓                        ↓              ↓
   Visualizations ←→ JSON Endpoints ←→ Context Manager
        ↓                        ↓              ↓
   Real-time UI ←→ WebSocket (future) ←→ Consciousness Monitor
```

## 🔧 Extension Points

### 1. Custom Cognitive Analyzers
```python
class CustomCognitiveAnalyzer:
    def analyze(self, content: str, context: List[ContextChunk]) -> CognitiveSignature:
        # Custom analysis logic
        pass
```

### 2. Custom Consciousness States
```python
class CustomConsciousnessState(Enum):
    DEBUGGING = "debugging"
    TEACHING = "teaching"
    RESEARCHING = "researching"
```

### 3. Custom Storage Backends
```python
class CustomStorageBackend:
    def store_chunk(self, chunk: ContextChunk):
        # Custom storage logic
        pass
```

## 📈 Performance Considerations

### Memory Management
- **Lazy Loading**: Load archived content on demand
- **Compression**: Compress similar content
- **Cleanup**: Automatic cleanup of unused embeddings
- **Caching**: Cache frequently accessed chunks

### Computational Efficiency
- **Batch Processing**: Process multiple queries together
- **Parallel Execution**: Multi-threaded consciousness simulation
- **Optimized Algorithms**: Efficient similarity calculations
- **Smart Indexing**: Fast semantic search with proper indexing

---

**Next**: Learn about the [Consciousness Model](./consciousness-model.md) in detail.
