"""
Context Window Manager Launcher
Easy startup script for the LMStudio Context Window Manager
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# Import requests conditionally since it might not be installed yet
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['numpy', 'requests', 'flask']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False
    
    return True

def check_lmstudio_connection(url="http://localhost:1234"):
    """Check if LMStudio is running and accessible"""
    if not REQUESTS_AVAILABLE:
        print("⚠️  Cannot check LMStudio connection (requests not installed)")
        print("   LMStudio check will be skipped")
        return False
    
    try:
        response = requests.get(f"{url}/v1/models", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ LMStudio is running with {len(models.get('data', []))} models")
            return True
        else:
            print(f"⚠️  LMStudio responded but with status {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("❌ LMStudio is not accessible at http://localhost:1234")
        print("   Please start LMStudio and enable the API server")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ['templates', 'exports']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Directory '{directory}' ready")

def show_startup_menu():
    """Show startup options menu"""
    print("\n" + "="*60)
    print("🧠 CONTEXT WINDOW MANAGER FOR LMSTUDIO")
    print("="*60)
    print("\nChoose how you want to start:")
    print("1. 🌐 Web Interface (Recommended)")
    print("2. 💻 Interactive Demo")
    print("3. 🔧 Real-world Integration Example")
    print("4. 📊 Just Test the Core System")
    print("5. ❓ Show Help & Documentation")
    print("0. 🚪 Exit")
    
    while True:
        try:
            choice = input("\nEnter your choice (0-5): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5']:
                return choice
            else:
                print("Invalid choice. Please enter 0-5.")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            return '0'

def launch_web_interface():
    """Launch the web interface"""
    print("\n🌐 Starting Web Interface...")
    print("🔗 Open http://localhost:5000 in your browser")
    print("🛑 Press Ctrl+C to stop the server")
    
    try:
        subprocess.run([sys.executable, 'web_interface.py'])
    except KeyboardInterrupt:
        print("\n🛑 Web interface stopped")
    except FileNotFoundError:
        print("❌ web_interface.py not found")

def launch_demo():
    """Launch the interactive demo"""
    print("\n💻 Starting Interactive Demo...")
    try:
        subprocess.run([sys.executable, 'demo_context_manager.py'])
    except KeyboardInterrupt:
        print("\n🛑 Demo stopped")
    except FileNotFoundError:
        print("❌ demo_context_manager.py not found")

def launch_integration_example():
    """Launch the integration example"""
    print("\n🔧 Starting Integration Example...")
    try:
        subprocess.run([sys.executable, 'lmstudio_integration.py'])
    except KeyboardInterrupt:
        print("\n🛑 Integration example stopped")
    except FileNotFoundError:
        print("❌ lmstudio_integration.py not found")

def test_core_system():
    """Test the core context management system"""
    print("\n📊 Testing Core System...")
    
    try:
        from context_manager import ContextWindowManager, ConversationSimulator
        
        print("✅ Importing context manager...")
        manager = ContextWindowManager(
            max_window_size=2000,
            rolling_window_size=5,
            lmstudio_url="http://localhost:1234"
        )
        
        print("✅ Context manager initialized")
        
        # Add some test messages
        test_messages = [
            ("Hello, how are you?", "user"),
            ("I'm doing well, thank you for asking!", "assistant"),
            ("Can you help me with Python programming?", "user"),
            ("Of course! I'd be happy to help with Python.", "assistant"),
            ("What's the difference between lists and tuples?", "user")
        ]
        
        print("✅ Adding test messages...")
        for content, role in test_messages:
            manager.add_message(content, role)
        
        # Test semantic retrieval
        print("✅ Testing semantic retrieval...")
        context = manager.get_optimized_context("Tell me about Python data structures")
        print(f"✅ Retrieved {len(context)} context messages")
        
        # Show analysis
        analysis = manager.analyze_conversation_patterns()
        print(f"✅ Analysis complete: {analysis['total_messages']} total messages")
        
        # Export test
        filename = manager.export_conversation("test_export.json")
        print(f"✅ Test conversation exported to: {filename}")
        
        print("\n🎉 Core system test completed successfully!")
        
    except Exception as e:
        print(f"❌ Core system test failed: {e}")

def show_help():
    """Show help and documentation"""
    print("\n📚 HELP & DOCUMENTATION")
    print("="*50)
    print("""
🧠 Context Window Manager Features:
  • Rolling Window: Keeps recent conversation context
  • Semantic Search: Finds relevant past conversations
  • Context Optimization: Smart token management
  • Persistent Memory: Saves and recalls conversations
  • LMStudio Integration: Works with your local models

📁 Files Overview:
  • context_manager.py - Core context management system
  • demo_context_manager.py - Interactive demonstration
  • lmstudio_integration.py - Real-world usage examples
  • web_interface.py - Web-based interface
  • README.md - Complete documentation

🚀 Quick Start:
  1. Make sure LMStudio is running on http://localhost:1234
  2. Choose option 1 for the web interface (recommended)
  3. Start chatting and see the intelligent context management

🔧 Configuration:
  • Edit the Python files to adjust context window sizes
  • Change LMStudio URL if using different port
  • Modify importance thresholds for archiving

📊 Monitoring:
  • Web interface shows real-time context statistics
  • Export conversations for analysis
  • View conversation timelines and patterns

For detailed documentation, check README.md
    """)

def main():
    """Main launcher function"""
    print("🚀 Context Window Manager Launcher")
    print("==================================")
    
    # Check system requirements
    if not check_python_version():
        return
    
    if not check_dependencies():
        return
    
    # Check LMStudio (optional for demo)
    lmstudio_available = check_lmstudio_connection()
    if not lmstudio_available:
        print("\n⚠️  LMStudio not detected. You can still run demos with simulated responses.")
    
    # Create directories
    create_directories()
    
    # Show menu and handle choice
    while True:
        choice = show_startup_menu()
        
        if choice == '0':
            print("\n👋 Thanks for using Context Window Manager!")
            break
        elif choice == '1':
            launch_web_interface()
        elif choice == '2':
            launch_demo()
        elif choice == '3':
            launch_integration_example()
        elif choice == '4':
            test_core_system()
        elif choice == '5':
            show_help()
        
        # Ask if user wants to continue
        if choice != '5':  # Don't ask after showing help
            continue_choice = input("\nWould you like to try something else? (y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes']:
                print("\n👋 Thanks for using Context Window Manager!")
                break

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please check the README.md for troubleshooting help.")