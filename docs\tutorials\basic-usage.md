# 📚 Basic Usage Tutorial

Learn how to use VORTEX step-by-step, from simple context management to consciousness-aware interactions.

## 🎯 Learning Objectives

By the end of this tutorial, you'll be able to:
- Set up and configure VORTEX for your needs
- Use traditional context management features
- Leverage consciousness simulation for enhanced interactions
- Monitor and analyze conversation patterns
- Export and manage conversation data

## 📋 Prerequisites

- VORTEX installed (see [Installation Guide](../installation.md))
- LMStudio running (optional but recommended)
- Basic Python knowledge

## 🚀 Part 1: Traditional Context Management

### Step 1: Basic Setup

```python
from context_manager import ContextWindowManager

# Initialize with basic settings
manager = ContextWindowManager(
    max_window_size=4096,      # Adjust to your model's context limit
    rolling_window_size=8,     # Keep 8 recent messages
    lmstudio_url="http://localhost:1234"  # Your LMStudio URL
)

print("Context manager initialized!")
```

### Step 2: Adding Messages

```python
# Add a user message
user_chunk = manager.add_message(
    content="I'm learning about machine learning. Can you help?",
    role="user",
    importance=1.0  # High importance (0.0 to 1.0)
)

print(f"Added message with ID: {user_chunk.id}")
print(f"Message has embedding: {user_chunk.embedding is not None}")

# Add an assistant response
assistant_chunk = manager.add_message(
    content="I'd be happy to help you learn machine learning! Let's start with the basics. Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed for every scenario.",
    role="assistant",
    importance=0.9
)

print(f"Conversation now has {len(manager.rolling_window)} messages in rolling window")
```

### Step 3: Getting Optimized Context

```python
# Get context for a new query
query = "What are the main types of machine learning?"
context = manager.get_optimized_context(query)

print(f"Optimized context contains {len(context)} messages:")
for i, message in enumerate(context):
    print(f"{i+1}. {message['role']}: {message['content'][:50]}...")
```

### Step 4: Checking System Status

```python
# Get current status
status = manager.get_status()

print("📊 System Status:")
print(f"  Total chunks: {status['total_chunks']}")
print(f"  Rolling window size: {status['rolling_window_size']}")
print(f"  Archived chunks: {status['archived_chunks']}")
print(f"  LMStudio connected: {status['lmstudio_connected']}")
print(f"  Semantic retrievals: {status['semantic_retrievals']}")
```

## 🧠 Part 2: VORTEX Consciousness System

### Step 1: Initialize VORTEX Core

```python
from vortex_core import VortexCore

# Initialize VORTEX with consciousness simulation
vortex = VortexCore(
    max_context_tokens=4096,
    working_memory_size=7,     # Miller's magic number
    lmstudio_url="http://localhost:1234"
)

print("VORTEX Core initialized!")
```

### Step 2: Start Consciousness Simulation

```python
# Start the background consciousness thread
vortex.start_consciousness_simulation()

print("Consciousness simulation started!")
print("The system is now actively monitoring and analyzing patterns...")
```

### Step 3: Add Context with Cognitive Analysis

```python
# Add content and see cognitive analysis
chunk1 = vortex.add_context(
    "I'm fascinated by the creative potential of AI in art and music",
    "user"
)

# Examine the cognitive signature
sig = chunk1.cognitive_signature
print("🧬 Cognitive Signature Analysis:")
print(f"  Emotion valence: {sig.emotion_valence:.3f} (-1=negative, 1=positive)")
print(f"  Complexity level: {sig.complexity_level:.3f} (0=simple, 1=complex)")
print(f"  Abstraction level: {sig.abstraction_level:.3f} (0=concrete, 1=abstract)")
print(f"  Creativity index: {sig.creativity_index:.3f} (0=routine, 1=creative)")
print(f"  Logical coherence: {sig.logical_coherence:.3f} (0=illogical, 1=logical)")
print(f"  Temporal urgency: {sig.temporal_urgency:.3f} (0=relaxed, 1=urgent)")
print(f"  Cognitive load: {sig.cognitive_load:.3f} (0=light, 1=heavy)")
```

### Step 4: Monitor Consciousness State

```python
import time

# Add more context to trigger state changes
vortex.add_context(
    "AI-generated art raises fascinating questions about creativity, authorship, and the nature of artistic expression. Can a machine truly be creative, or is it simply recombining existing patterns in novel ways?",
    "assistant"
)

vortex.add_context(
    "That's a profound philosophical question. What do you think defines true creativity?",
    "user"
)

# Wait for consciousness processing
time.sleep(3)

# Check consciousness state
report = vortex.get_consciousness_report()
print("🧠 Consciousness Report:")
print(f"  Current state: {report['current_state']}")
print(f"  Consciousness active: {report['consciousness_active']}")
print(f"  Working memory size: {report['working_memory_size']}")

# Check self-monitoring metrics
monitoring = report['self_monitoring']
print("🔍 Self-Monitoring:")
print(f"  Confidence level: {monitoring['confidence_level']:.3f}")
print(f"  Cognitive load: {monitoring['cognitive_load']:.3f}")
print(f"  Learning momentum: {monitoring['learning_momentum']:.3f}")
print(f"  Attention stability: {monitoring['attention_stability']:.3f}")
```

### Step 5: Consciousness-Aware Context Retrieval

```python
# Get consciousness-optimized context
query = "How can we measure creativity in AI systems?"
context = vortex.get_optimized_context(query)

print(f"🌀 Consciousness-optimized context ({len(context)} messages):")
for i, message in enumerate(context):
    print(f"{i+1}. {message['role']}: {message['content'][:60]}...")

# The context selection is now influenced by:
# - Current consciousness state (likely CREATIVE or REFLECTIVE)
# - Cognitive signatures of stored content
# - Semantic relevance to the query
# - Importance and recency
```

## 📊 Part 3: Analysis and Monitoring

### Step 1: Conversation Pattern Analysis

```python
# Analyze conversation patterns (works with both systems)
analysis = manager.analyze_conversation_patterns()

print("📈 Conversation Analysis:")
print(f"  Total messages: {analysis['total_messages']}")
print(f"  Average message length: {analysis['avg_message_length']:.1f} characters")
print(f"  Most active role: {analysis['most_active_role']}")
print(f"  Conversation duration: {analysis['conversation_duration']:.1f} seconds")

# Most accessed content
print("\n🔥 Most Accessed Content:")
for content, count in analysis['most_accessed_content'][:3]:
    print(f"  '{content[:50]}...' (accessed {count} times)")
```

### Step 2: Semantic Search

```python
# Search for semantically similar content
results = manager.search_semantic("artificial intelligence creativity", limit=3)

print("🔍 Semantic Search Results:")
for chunk, similarity in results:
    print(f"  Similarity: {similarity:.3f}")
    print(f"  Content: {chunk.content[:80]}...")
    print(f"  Role: {chunk.role}, Importance: {chunk.importance}")
    print()
```

### Step 3: Export and Save

```python
# Export conversation for later analysis
filename = manager.export_conversation()
print(f"💾 Conversation exported to: {filename}")

# The export includes:
# - All conversation messages
# - Metadata and timestamps
# - System configuration
# - Analysis results
# - Statistics
```

## 🎨 Part 4: Practical Examples

### Example 1: Research Assistant Session

```python
# Simulate a research session
research_manager = ContextWindowManager(
    max_window_size=6000,      # Larger context for research
    rolling_window_size=12,    # Keep more recent context
    importance_threshold=0.7   # Archive more content
)

# Research conversation
research_manager.add_message("I'm researching quantum computing applications", "user")
research_manager.add_message("Quantum computing has several promising applications including cryptography, optimization, and drug discovery...", "assistant")
research_manager.add_message("Can you elaborate on the drug discovery applications?", "user")
research_manager.add_message("In drug discovery, quantum computers could simulate molecular interactions...", "assistant")

# Later, ask related questions
context = research_manager.get_optimized_context("How does quantum simulation help with molecular modeling?")
print(f"Research context includes {len(context)} relevant messages")
```

### Example 2: Creative Writing Session

```python
# Initialize VORTEX for creative writing
creative_vortex = VortexCore(max_context_tokens=8000)
creative_vortex.start_consciousness_simulation()

# Creative writing conversation
creative_vortex.add_context("I'm writing a science fiction story about time travel", "user")
creative_vortex.add_context("Time travel stories offer rich possibilities for exploring paradoxes and consequences...", "assistant")
creative_vortex.add_context("I want to explore the grandfather paradox in my story", "user")

# Check if consciousness shifted to CREATIVE state
time.sleep(2)
report = creative_vortex.get_consciousness_report()
print(f"Consciousness state for creative writing: {report['current_state']}")

# Get creative context
context = creative_vortex.get_optimized_context("How can I make the time travel mechanics unique?")
```

### Example 3: Technical Support Session

```python
# Technical support configuration
support_manager = ContextWindowManager(
    max_window_size=4000,
    rolling_window_size=6,     # Shorter window for focused support
    importance_threshold=0.9   # Only archive very important content
)

# Support conversation
support_manager.add_message("My Python script is throwing a KeyError", "user")
support_manager.add_message("A KeyError occurs when trying to access a dictionary key that doesn't exist...", "assistant")
support_manager.add_message("Here's my code: data['missing_key']", "user")

# Get focused context for troubleshooting
context = support_manager.get_optimized_context("How do I handle missing keys safely?")
```

## 🔧 Part 5: Customization and Configuration

### Custom Importance Scoring

```python
def custom_importance_scorer(content: str, role: str) -> float:
    """Custom function to score message importance"""
    importance = 0.5  # Base importance
    
    # Increase importance for questions
    if '?' in content:
        importance += 0.2
    
    # Increase importance for code
    if 'def ' in content or 'class ' in content:
        importance += 0.3
    
    # Increase importance for user messages
    if role == "user":
        importance += 0.1
    
    return min(1.0, importance)

# Use custom scoring
chunk = manager.add_message(
    "def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
    "user",
    importance=custom_importance_scorer("def fibonacci...", "user")
)
```

### Configuration Profiles

```python
# Different configurations for different use cases
CONFIGS = {
    'research': {
        'max_window_size': 8000,
        'rolling_window_size': 15,
        'importance_threshold': 0.6
    },
    'casual_chat': {
        'max_window_size': 3000,
        'rolling_window_size': 6,
        'importance_threshold': 0.8
    },
    'code_assistance': {
        'max_window_size': 6000,
        'rolling_window_size': 10,
        'importance_threshold': 0.7
    }
}

# Initialize with a profile
config = CONFIGS['research']
research_manager = ContextWindowManager(**config)
```

## 🎯 Next Steps

Now that you've mastered the basics:

1. **Explore Advanced Features**: Check out [Advanced Features Tutorial](./advanced-features.md)
2. **Try Integration Examples**: See [Integration Examples](./integration-examples.md)
3. **Learn About Consciousness**: Study [Consciousness Model](../consciousness-model.md)
4. **Build Custom Extensions**: Follow [Custom Extensions](./custom-extensions.md)

## 💡 Best Practices

1. **Start Simple**: Begin with basic context management, add consciousness features gradually
2. **Monitor Performance**: Use status and analysis functions regularly
3. **Adjust Configuration**: Tune parameters based on your specific use case
4. **Export Regularly**: Save important conversations for later analysis
5. **Experiment**: Try different consciousness states and cognitive signatures

## 🚨 Common Pitfalls

1. **Too Large Context**: Don't set max_window_size larger than your model can handle
2. **Ignoring Importance**: Use importance scoring to prioritize valuable content
3. **Not Monitoring**: Check consciousness state and system status regularly
4. **Forgetting Cleanup**: Clear conversations when starting new topics

---

**Congratulations!** You now have a solid foundation in using VORTEX. Continue with the [Advanced Features Tutorial](./advanced-features.md) to unlock more powerful capabilities.
