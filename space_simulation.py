import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from mpl_toolkits.mplot3d import Axes3D
import tkinter as tk
from tkinter import ttk
import threading
import time
from collections import deque

# Physics constants
class PhysicsConstants:
    G = 6.674e-11  # Gravitational constant (scaled for simulation)
    C = 299792458  # Speed of light
    MIN_DISTANCE = 0.1  # Minimum distance to prevent singularities
    TIDAL_FORCE_SCALE = 0.1
    SCHWARZSCHILD_SCALE = 0.001
    GRAVITATIONAL_WAVE_SCALE = 1e-15
    ROCHE_LIMIT_FACTOR = 2.44

class Vector3D:
    """3D Vector class with all necessary operations"""
    def __init__(self, x=0.0, y=0.0, z=0.0):
        if isinstance(x, (list, tuple, np.ndarray)):
            self.data = np.array(x, dtype=float)
        else:
            self.data = np.array([x, y, z], dtype=float)
    
    @property
    def x(self): return self.data[0]
    @property
    def y(self): return self.data[1]
    @property
    def z(self): return self.data[2]
    
    @x.setter
    def x(self, value): self.data[0] = value
    @y.setter
    def y(self, value): self.data[1] = value
    @z.setter
    def z(self, value): self.data[2] = value
    
    def __add__(self, other):
        return Vector3D(self.data + other.data)
    
    def __sub__(self, other):
        return Vector3D(self.data - other.data)
    
    def __mul__(self, scalar):
        return Vector3D(self.data * scalar)
    
    def __truediv__(self, scalar):
        return Vector3D(self.data / scalar)
    
    def magnitude(self):
        return np.linalg.norm(self.data)
    
    def normalize(self):
        mag = self.magnitude()
        if mag == 0:
            return Vector3D(0, 0, 0)
        return Vector3D(self.data / mag)
    
    def dot(self, other):
        return np.dot(self.data, other.data)
    
    def cross(self, other):
        return Vector3D(np.cross(self.data, other.data))
    
    def distance_to(self, other):
        return (self - other).magnitude()
    
    def copy(self):
        return Vector3D(self.data.copy())

class CelestialBody:
    """Represents a celestial body with advanced physics"""
    def __init__(self, position=None, velocity=None, mass=1.0, radius=1.0, 
                 color='blue', body_type='planet', fixed=False, name=None):
        self.position = position or Vector3D()
        self.velocity = velocity or Vector3D()
        self.acceleration = Vector3D()
        self.mass = mass
        self.radius = radius
        self.color = color
        self.body_type = body_type  # 'planet', 'sun', 'asteroid', 'moon'
        self.fixed = fixed
        self.name = name or f"{body_type}_{id(self)}"
        
        # Physics properties
        self.density = mass / (4/3 * np.pi * radius**3)
        self.kinetic_energy = 0.0
        self.potential_energy = 0.0
        self.total_energy = 0.0
        
        # Trail for visualization
        self.trail = deque(maxlen=1000)
        self.trail.append(self.position.copy())
        
        # Advanced physics properties
        self.tidal_deformation = 0.0
        self.original_radius = radius
        
    def apply_force(self, force):
        """Apply a force to the body"""
        if not self.fixed:
            acceleration = force / self.mass
            self.acceleration = self.acceleration + acceleration
    
    def apply_tidal_force(self, other):
        """Apply tidal forces from another massive body"""
        if self.fixed:
            return
            
        direction = other.position - self.position
        distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
        
        # Tidal force calculation
        tidal_strength = (PhysicsConstants.TIDAL_FORCE_SCALE * 
                         other.mass * self.mass / (distance**3))
        tidal_force = direction.normalize() * tidal_strength
        
        # Apply tidal deformation
        deformation = tidal_strength * 0.01
        self.radius = self.original_radius * (1 + deformation * 0.1)
        self.tidal_deformation = deformation
        
        self.apply_force(tidal_force)
    
    def apply_relativistic_corrections(self, other):
        """Apply relativistic corrections for massive objects"""
        if self.fixed or other.mass < 50:
            return
            
        direction = other.position - self.position
        distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
        speed = self.velocity.magnitude()
        
        # Simple relativistic correction
        beta = speed / PhysicsConstants.C
        gamma = 1 / np.sqrt(1 - beta**2) if beta < 0.9 else 10
        correction = (gamma - 1) * 0.01
        
        relativistic_force = (direction.normalize() * correction * 
                            other.mass * PhysicsConstants.G / distance**2)
        self.apply_force(relativistic_force)
    
    def is_within_roche_limit(self, other):
        """Check if within Roche limit of another body"""
        distance = self.position.distance_to(other.position)
        roche_limit = (PhysicsConstants.ROCHE_LIMIT_FACTOR * other.radius * 
                      (other.mass / self.mass)**(1/3))
        return distance < roche_limit
    
    def update(self, dt):
        """Update position and velocity using Verlet integration"""
        if self.fixed:
            return
            
        # Verlet integration for stable orbits
        new_position = (self.position + self.velocity * dt + 
                       self.acceleration * (0.5 * dt**2))
        
        # Update position
        self.position = new_position
        
        # Update velocity (will be corrected in next physics step)
        self.velocity = self.velocity + self.acceleration * dt
        
        # Reset acceleration for next step
        self.acceleration = Vector3D()
        
        # Update trail
        if len(self.trail) == 0 or self.position.distance_to(self.trail[-1]) > 0.1:
            self.trail.append(self.position.copy())
        
        # Calculate energies
        self.calculate_energies()
    
    def calculate_energies(self):
        """Calculate kinetic energy"""
        velocity_magnitude = self.velocity.magnitude()
        self.kinetic_energy = 0.5 * self.mass * velocity_magnitude**2
    
    def get_orbital_parameters(self, central_body):
        """Calculate orbital parameters relative to a central body"""
        if not central_body:
            return None
            
        r = self.position - central_body.position
        v = self.velocity - central_body.velocity
        distance = r.magnitude()
        speed = v.magnitude()
        
        # Standard gravitational parameter
        mu = PhysicsConstants.G * (self.mass + central_body.mass)
        
        # Specific orbital energy
        energy = 0.5 * speed**2 - mu / distance
        
        # Semi-major axis
        semi_major_axis = -mu / (2 * energy) if energy < 0 else float('inf')
        
        # Angular momentum
        h = r.cross(v)
        angular_momentum = h.magnitude()
        
        # Eccentricity
        if angular_momentum > 0 and energy < 0:
            eccentricity = np.sqrt(1 + 2 * energy * angular_momentum**2 / mu**2)
        else:
            eccentricity = 0
            
        # Orbital period
        if semi_major_axis > 0 and semi_major_axis != float('inf'):
            period = 2 * np.pi * np.sqrt(semi_major_axis**3 / mu)
        else:
            period = float('inf')
            
        return {
            'semi_major_axis': semi_major_axis,
            'eccentricity': eccentricity,
            'period': period,
            'distance': distance,
            'speed': speed,
            'angular_momentum': angular_momentum,
            'energy': energy
        }

class GravityField:
    """Represents gravitational field visualization"""
    def __init__(self, bodies, field_size=50, resolution=20):
        self.bodies = bodies
        self.field_size = field_size
        self.resolution = resolution
        self.field_points = []
        self.field_strengths = []
        self.update_field()
    
    def update_field(self):
        """Update gravitational field visualization"""
        self.field_points = []
        self.field_strengths = []
        
        step = self.field_size / self.resolution
        for x in np.arange(-self.field_size/2, self.field_size/2, step):
            for z in np.arange(-self.field_size/2, self.field_size/2, step):
                position = Vector3D(x, 0, z)
                potential = self.calculate_potential(position)
                field_strength = abs(potential) * 100  # Scale for visibility
                
                if field_strength > 0.01:
                    self.field_points.append([x, 0, z])
                    self.field_strengths.append(field_strength)
    
    def calculate_potential(self, position):
        """Calculate gravitational potential at a point"""
        potential = 0
        for body in self.bodies:
            distance = max(position.distance_to(body.position), 
                          PhysicsConstants.MIN_DISTANCE)
            potential -= PhysicsConstants.G * body.mass / distance
        return potential

class PhysicsSystem:
    """Advanced N-body physics system"""
    def __init__(self):
        self.bodies = []
        self.gravitational_constant = 1.0
        self.time_scale = 1.0
        self.enable_tidal_forces = True
        self.enable_relativistic_effects = False
        self.enable_gravitational_waves = False
        self.tidal_force_strength = 1.0
        self.total_energy = 0.0
        self.frame_count = 0
        
    def add_body(self, body):
        """Add a celestial body to the system"""
        self.bodies.append(body)
    
    def remove_body(self, body):
        """Remove a celestial body from the system"""
        if body in self.bodies:
            self.bodies.remove(body)
    
    def clear(self):
        """Clear all bodies"""
        self.bodies.clear()
    
    def calculate_gravitational_forces(self):
        """Calculate advanced gravitational forces between all bodies"""
        # Reset accelerations
        for body in self.bodies:
            body.acceleration = Vector3D()
        
        # Calculate pairwise forces
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                
                direction = body2.position - body1.position
                distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
                
                # Basic gravitational force with distance softening
                softening_length = 0.1
                softened_distance = np.sqrt(distance**2 + softening_length**2)
                force_magnitude = (PhysicsConstants.G * self.gravitational_constant * 
                                 body1.mass * body2.mass / softened_distance**2)
                
                # Add tidal gradient effects for close bodies
                if distance < (body1.radius + body2.radius) * 5:
                    tidal_factor = 1 + 0.1 * (body1.radius + body2.radius) / distance
                    force_magnitude *= tidal_factor
                
                force_vector = direction.normalize() * force_magnitude
                
                # Apply Newton's third law
                body1.apply_force(force_vector)
                body2.apply_force(force_vector * -1)
                
                # Apply advanced physics effects
                if self.enable_tidal_forces and distance < (body1.radius + body2.radius) * 10:
                    body1.apply_tidal_force(body2)
                    body2.apply_tidal_force(body1)
                
                if self.enable_relativistic_effects:
                    body1.apply_relativistic_corrections(body2)
                    body2.apply_relativistic_corrections(body1)
                
                # Handle Roche limit violations
                if (body1.is_within_roche_limit(body2) or 
                    body2.is_within_roche_limit(body1)):
                    self.handle_roche_limit_violation(body1, body2)
        
        # Apply gravitational wave effects
        if self.enable_gravitational_waves:
            self.apply_gravitational_wave_effects()
    
    def handle_roche_limit_violation(self, body1, body2):
        """Handle tidal disruption within Roche limit"""
        # Determine which body gets disrupted (smaller one)
        disrupted = body1 if body1.mass < body2.mass else body2
        
        # Add instability
        instability_force = Vector3D(
            np.random.uniform(-0.1, 0.1),
            np.random.uniform(-0.1, 0.1),
            np.random.uniform(-0.1, 0.1)
        ) * disrupted.mass
        
        disrupted.apply_force(instability_force)
        
        # Tidal stripping
        disrupted.radius *= 0.999
    
    def apply_gravitational_wave_effects(self):
        """Apply gravitational wave energy loss"""
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                
                distance = body1.position.distance_to(body2.position)
                total_mass = body1.mass + body2.mass
                reduced_mass = (body1.mass * body2.mass) / total_mass
                
                # Gravitational wave energy loss
                if distance < 50 and total_mass > 10:
                    gw_energy_loss = (PhysicsConstants.GRAVITATIONAL_WAVE_SCALE * 
                                    (reduced_mass * total_mass**2)**2 / distance**5)
                    
                    direction = (body2.position - body1.position).normalize()
                    gw_force = direction * gw_energy_loss
                    
                    body1.apply_force(gw_force)
                    body2.apply_force(gw_force * -1)
    
    def find_lagrange_points(self, body1, body2):
        """Find Lagrange points between two bodies"""
        if body1.mass < body2.mass * 0.01:
            return []
            
        separation = body1.position.distance_to(body2.position)
        mass_ratio = body1.mass / body2.mass
        
        # L1 point (between bodies)
        l1_distance = separation * (1 - (mass_ratio / 3)**(1/3))
        direction = (body2.position - body1.position).normalize()
        l1_position = body1.position + direction * l1_distance
        
        # L2 point (beyond smaller body)
        l2_distance = separation * (1 + (mass_ratio / 3)**(1/3))
        l2_position = body1.position + direction * l2_distance
        
        return [
            {'type': 'L1', 'position': l1_position},
            {'type': 'L2', 'position': l2_position}
        ]
    
    def update(self, dt):
        """Update the physics system"""
        scaled_dt = dt * self.time_scale
        
        # Calculate forces
        self.calculate_gravitational_forces()
        
        # Update all bodies
        for body in self.bodies:
            body.update(scaled_dt)
        
        # Calculate system energy
        self.calculate_system_energy()
        self.frame_count += 1
    
    def calculate_system_energy(self):
        """Calculate total system energy"""
        total_kinetic = sum(body.kinetic_energy for body in self.bodies)
        total_potential = 0
        
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                distance = max(body1.position.distance_to(body2.position),
                             PhysicsConstants.MIN_DISTANCE)
                potential = -PhysicsConstants.G * body1.mass * body2.mass / distance
                total_potential += potential
        
        self.total_energy = total_kinetic + total_potential
        return self.total_energy
    
    def get_center_of_mass(self):
        """Calculate center of mass of the system"""
        total_mass = sum(body.mass for body in self.bodies)
        if total_mass == 0:
            return Vector3D()
            
        com_position = Vector3D()
        for body in self.bodies:
            com_position = com_position + body.position * body.mass
        
        return com_position / total_mass
    
    def create_stable_orbit(self, central_body, orbiting_body, distance, velocity=None):
        """Create a stable circular orbit"""
        if velocity is None:
            mu = PhysicsConstants.G * self.gravitational_constant * central_body.mass
            orbital_velocity = np.sqrt(mu / distance)
        else:
            orbital_velocity = velocity
            
        # Position orbiting body
        orbiting_body.position = central_body.position + Vector3D(distance, 0, 0)
        orbiting_body.velocity = central_body.velocity + Vector3D(0, orbital_velocity, 0)

if __name__ == "__main__":
    # Test the physics system
    physics = PhysicsSystem()
    
    # Create a simple sun-planet system
    sun = CelestialBody(
        position=Vector3D(0, 0, 0),
        velocity=Vector3D(0, 0, 0),
        mass=10,
        radius=3,
        color='yellow',
        body_type='sun',
        fixed=True,
        name='Sun'
    )
    
    planet = CelestialBody(
        position=Vector3D(15, 0, 0),
        velocity=Vector3D(0, 2.5, 0),
        mass=1,
        radius=1,
        color='blue',
        body_type='planet',
        name='Planet'
    )
    
    physics.add_body(sun)
    physics.add_body(planet)
    
    print("Space Physics System initialized successfully!")
    print(f"System has {len(physics.bodies)} bodies")
    print(f"Sun: {sun.name} at {sun.position.data}")
    print(f"Planet: {planet.name} at {planet.position.data}")