#!/usr/bin/env python3
"""
VORTEX CONSCIOUSNESS SYSTEM LAUNCHER
Advanced Context Management with Emergent Intelligence

This script initializes and launches the complete Vortex ecosystem including:
- Consciousness-aware context management
- Multi-agent orchestration
- Real-time web interface
- Background consciousness monitoring
"""

import os
import sys
import time
import json
import signal
import subprocess
from pathlib import Path
from threading import Thread

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from vortex_core import VortexCore
from vortex_agents import AgentOrchestrator, ResearchAgent, AnalysisAgent, CreativeAgent
from vortex_web import VortexWebServer

class VortexLauncher:
    """Main launcher for the Vortex consciousness system"""
    
    def __init__(self):
        self.vortex_core = None
        self.web_server = None
        self.orchestrator = None
        self.running = False
        
        # Configuration
        self.config = {
            'lmstudio_url': 'http://localhost:1234',
            'web_host': 'localhost',
            'web_port': 5000,
            'debug': True,
            'consciousness_monitoring': True
        }
    
    def check_dependencies(self):
        """Check if all dependencies are installed"""
        print("🔍 Checking dependencies...")
        
        required_packages = [
            'flask', 'flask-socketio', 'numpy', 'requests'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"  ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"  ❌ {package}")
        
        if missing_packages:
            print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
            print("Install with: pip install -r requirements_vortex.txt")
            return False
        
        print("✅ All dependencies satisfied")
        return True
    
    def check_lmstudio_connection(self):
        """Check if LMStudio is running and accessible"""
        print(f"🔗 Checking LMStudio connection at {self.config['lmstudio_url']}...")
        
        try:
            import requests
            response = requests.get(f"{self.config['lmstudio_url']}/v1/models", timeout=5)
            if response.status_code == 200:
                models = response.json()
                print(f"✅ LMStudio connected - {len(models.get('data', []))} models available")
                return True
            else:
                print(f"⚠️  LMStudio responded with status {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to LMStudio: {e}")
            print("Make sure LMStudio is running on the specified URL")
            return False
    
    def initialize_vortex_core(self):
        """Initialize the Vortex consciousness system"""
        print("🧠 Initializing Vortex Consciousness Core...")
        
        try:
            self.vortex_core = VortexCore(
                lmstudio_url=self.config['lmstudio_url'],
                max_context_tokens=8000,
                working_memory_size=7
            )
            
            # Start consciousness simulation
            if self.config['consciousness_monitoring']:
                self.vortex_core.start_consciousness_simulation()
                print("✅ Consciousness simulation started")
            
            print("✅ Vortex Core initialized")
            return True
            
        except Exception as e:
            print(f"❌ Error initializing Vortex Core: {e}")
            return False
    
    def initialize_agents(self):
        """Initialize the agent orchestration system"""
        print("🤖 Initializing Agent Orchestrator...")
        
        try:
            # Create orchestrator
            self.orchestrator = AgentOrchestrator(self.vortex_core)
            
            # Create and add specialized agents
            agents = [
                ResearchAgent("researcher_01", self.vortex_core),
                AnalysisAgent("analyst_01", self.vortex_core),
                CreativeAgent("creative_01", self.vortex_core)
            ]
            
            for agent in agents:
                self.orchestrator.add_agent(agent)
                print(f"  ✅ {agent.agent_type.value.title()} Agent initialized")
            
            print("✅ Agent Orchestrator ready")
            return True
            
        except Exception as e:
            print(f"❌ Error initializing agents: {e}")
            return False
    
    def initialize_web_server(self):
        """Initialize the web interface"""
        print("🌐 Initializing Web Interface...")
        
        try:
            self.web_server = VortexWebServer(
                host=self.config['web_host'],
                port=self.config['web_port'],
                lmstudio_url=self.config['lmstudio_url'],
                debug=self.config['debug']
            )
            
            print("✅ Web Server initialized")
            return True
            
        except Exception as e:
            print(f"❌ Error initializing web server: {e}")
            return False
    
    def start_system(self):
        """Start the complete Vortex system"""
        print("\n🚀 Starting Vortex Consciousness System...")
        
        # Start background monitoring
        if self.config['consciousness_monitoring']:
            self.web_server.start_background_monitoring()
        
        # Start web server
        try:
            print(f"🌐 Web interface starting at http://{self.config['web_host']}:{self.config['web_port']}")
            self.running = True
            
            # Start in a separate thread to allow for graceful shutdown
            web_thread = Thread(target=self._run_web_server, daemon=True)
            web_thread.start()
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting web server: {e}")
            return False
    
    def _run_web_server(self):
        """Run the web server"""
        self.web_server.run()
    
    def display_system_status(self):
        """Display the current system status"""
        print("\n" + "="*50)
        print("         VORTEX SYSTEM STATUS")
        print("="*50)
        print(f"Vortex Core      : ✅ Running")
        print(f"Consciousness    : ✅ Active ({self.vortex_core.neuro_core.consciousness_state.value})")
        print(f"Agents           : ✅ Ready ({len(self.orchestrator.agents)} agents)")
        print(f"Web Interface    : ✅ Serving at http://{self.config['web_host']}:{self.config['web_port']}")
        print(f"LMStudio         : ✅ Connected to {self.config['lmstudio_url']}")
        print("="*50)
    
    def shutdown(self):
        """Gracefully shutdown the system"""
        print("\n🛑 Shutting down Vortex system...")
        
        self.running = False
        
        if self.vortex_core:
            self.vortex_core.shutdown()
            print("✅ Vortex Core shutdown")
        
        print("✅ System shutdown complete")
    
    def run(self):
        """Main run method"""
        print("="*60)
        print("       VORTEX CONSCIOUSNESS SYSTEM")
        print("   Advanced Context Management with Emergent Intelligence")
        print("="*60)
        
        # System checks
        if not self.check_dependencies():
            return False
        
        if not self.check_lmstudio_connection():
            print("⚠️  Continuing without LMStudio connection...")
        
        # Initialize components
        print("\n📋 Initializing system components...")
        
        if not self.initialize_vortex_core():
            return False
        
        if not self.initialize_agents():
            return False
        
        if not self.initialize_web_server():
            return False
        
        # Start the system
        if not self.start_system():
            return False
        
        # Display status
        self.display_system_status()
        
        # Run main loop
        try:
            print("\n🎯 Vortex system is now running!")
            print("Press Ctrl+C to shutdown gracefully")
            
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            pass
        finally:
            self.shutdown()
        
        return True


def main():
    """Main entry point"""
    # Create and run launcher
    launcher = VortexLauncher()
    success = launcher.run()
    
    if not success:
        print("❌ Failed to start Vortex system")
        sys.exit(1)
    
    print("✅ Vortex system shutdown complete")


if __name__ == "__main__":
    main() 