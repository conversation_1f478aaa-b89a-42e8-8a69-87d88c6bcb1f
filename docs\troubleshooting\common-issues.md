# 🔧 Common Issues & Solutions

Quick solutions to frequently encountered problems with VORTEX.

## 🚨 Installation Issues

### Python Import Errors

#### Problem: `ModuleNotFoundError: No module named 'vortex_core'`

**Solution:**
```bash
# Make sure you're in the correct directory
cd /path/to/vortex
ls -la  # Should see vortex_core.py

# Check Python path
python -c "import sys; print(sys.path)"

# If needed, add current directory to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

#### Problem: `ModuleNotFoundError: No module named 'numpy'`

**Solution:**
```bash
# Install missing dependencies
pip install -r requirements_context.txt

# Or install individually
pip install numpy>=1.21.0 requests>=2.28.0

# For virtual environment users
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements_context.txt
```

#### Problem: `ImportError: cannot import name 'VortexCore'`

**Solution:**
```python
# Check if the file exists and has the class
python -c "import vortex_core; print(dir(vortex_core))"

# If Vortex<PERSON>ore is not listed, check the file
head -20 vortex_core.py

# Make sure the class definition exists
grep -n "class VortexCore" vortex_core.py
```

### Permission Errors

#### Problem: `PermissionError: [Errno 13] Permission denied`

**Solution:**
```bash
# Check file permissions
ls -la vortex_core.py

# Fix permissions if needed
chmod 644 *.py
chmod 755 .

# For database files
chmod 666 *.db
```

## 🌐 LMStudio Connection Issues

### Connection Refused

#### Problem: `ConnectionError: Connection refused to http://localhost:1234`

**Diagnosis:**
```bash
# Check if LMStudio is running
curl http://localhost:1234/v1/models

# Check if port is in use
netstat -an | grep 1234
# On Windows: netstat -an | findstr 1234

# Check if process is running
ps aux | grep lmstudio
# On Windows: tasklist | findstr lmstudio
```

**Solutions:**

1. **Start LMStudio:**
   - Launch LMStudio application
   - Go to "Local Server" tab
   - Load a model
   - Click "Start Server"

2. **Check Port Configuration:**
   ```python
   # Try different ports
   manager = ContextWindowManager(lmstudio_url="http://localhost:1235")
   
   # Or check LMStudio settings for the correct port
   ```

3. **Firewall Issues:**
   ```bash
   # Temporarily disable firewall (be careful!)
   # Or add exception for port 1234
   ```

### API Errors

#### Problem: `HTTP 404: Model not found`

**Solution:**
```python
# List available models
from context_manager import ContextWindowManager
manager = ContextWindowManager()
models = manager.lm_client.list_models()
print("Available models:", models)

# Use a valid model name
response = manager.lm_client.chat_completion(
    messages=[{"role": "user", "content": "Hello"}],
    model=models[0] if models else "default"
)
```

#### Problem: `HTTP 500: Internal Server Error`

**Solutions:**
1. **Restart LMStudio:**
   - Close LMStudio completely
   - Restart the application
   - Reload your model

2. **Check Model Status:**
   - Ensure model is fully loaded
   - Check LMStudio logs for errors
   - Try a different model

3. **Reduce Request Size:**
   ```python
   # Reduce context size
   manager = ContextWindowManager(max_window_size=2000)
   
   # Or reduce message length
   short_message = long_message[:500]
   ```

## 💾 Database Issues

### Database Locked

#### Problem: `sqlite3.OperationalError: database is locked`

**Solutions:**

1. **Close Other Connections:**
   ```python
   # Make sure to properly shutdown VORTEX
   vortex.shutdown()
   
   # Wait a moment before restarting
   import time
   time.sleep(2)
   ```

2. **Remove Lock File:**
   ```bash
   # Remove database lock (be careful!)
   rm vortex_consciousness.db-wal
   rm vortex_consciousness.db-shm
   ```

3. **Use Different Database:**
   ```python
   # Use a different database file
   vortex = VortexCore(db_path="./new_vortex.db")
   ```

### Database Corruption

#### Problem: `sqlite3.DatabaseError: database disk image is malformed`

**Solutions:**

1. **Backup and Recreate:**
   ```bash
   # Backup existing database
   cp vortex_consciousness.db vortex_consciousness.db.backup
   
   # Remove corrupted database
   rm vortex_consciousness.db
   
   # VORTEX will create a new one
   ```

2. **Repair Database:**
   ```bash
   # Try to repair with sqlite3
   sqlite3 vortex_consciousness.db ".dump" | sqlite3 repaired.db
   mv repaired.db vortex_consciousness.db
   ```

## 🧠 Consciousness Simulation Issues

### Thread Not Starting

#### Problem: Consciousness simulation not starting

**Diagnosis:**
```python
vortex = VortexCore()
vortex.start_consciousness_simulation()

# Check if thread is running
report = vortex.get_consciousness_report()
print(f"Consciousness active: {report['consciousness_active']}")

# Check for errors
import time
time.sleep(3)
report = vortex.get_consciousness_report()
```

**Solutions:**

1. **Check Threading:**
   ```python
   import threading
   print(f"Active threads: {threading.active_count()}")
   print(f"Thread list: {[t.name for t in threading.enumerate()]}")
   ```

2. **Manual Start:**
   ```python
   # Try starting manually
   vortex.running = True
   vortex._consciousness_loop()  # This will block, use for debugging
   ```

3. **Disable Consciousness:**
   ```python
   # Use without consciousness simulation
   vortex = VortexCore()
   # Don't call start_consciousness_simulation()
   ```

### High CPU Usage

#### Problem: Consciousness simulation using too much CPU

**Solutions:**

1. **Increase Sleep Time:**
   ```python
   # Modify the consciousness loop (in vortex_core.py)
   # Change: time.sleep(2)
   # To: time.sleep(5)  # or higher
   ```

2. **Reduce Processing:**
   ```python
   # Reduce working memory size
   vortex = VortexCore(working_memory_size=3)
   
   # Or disable insights generation temporarily
   ```

## 🧮 Memory Issues

### High Memory Usage

#### Problem: VORTEX using too much RAM

**Diagnosis:**
```python
import psutil
import os

# Check memory usage
process = psutil.Process(os.getpid())
memory_mb = process.memory_info().rss / 1024 / 1024
print(f"Memory usage: {memory_mb:.1f} MB")

# Check VORTEX statistics
status = vortex.get_consciousness_report()
print(f"Working memory size: {status['working_memory_size']}")
print(f"Total chunks: {status['statistics']['total_chunks_processed']}")
```

**Solutions:**

1. **Reduce Memory Limits:**
   ```python
   vortex = VortexCore(
       max_context_tokens=2000,    # Reduce from 8000
       working_memory_size=3       # Reduce from 7
   )
   ```

2. **Clear Old Data:**
   ```python
   # Clear conversation history
   vortex.working_memory.clear()
   vortex.episodic_memory.clear()
   
   # Or restart with fresh database
   vortex.shutdown()
   os.remove("vortex_consciousness.db")
   vortex = VortexCore()
   ```

3. **Optimize Embeddings:**
   ```python
   # Use smaller embeddings or disable them
   vortex = VortexCore(embedding_model=None)  # Disables embeddings
   ```

### Memory Leaks

#### Problem: Memory usage keeps growing

**Solutions:**

1. **Proper Cleanup:**
   ```python
   # Always shutdown properly
   try:
       # Your VORTEX code here
       pass
   finally:
       vortex.shutdown()
   ```

2. **Periodic Cleanup:**
   ```python
   # Add periodic cleanup
   import gc
   
   # Every 100 messages
   if vortex.stats['total_chunks_processed'] % 100 == 0:
       gc.collect()
   ```

## ⚡ Performance Issues

### Slow Response Times

#### Problem: VORTEX taking too long to respond

**Diagnosis:**
```python
import time

start_time = time.time()
chunk = vortex.add_context("Test message", "user")
add_time = time.time() - start_time

start_time = time.time()
context = vortex.get_optimized_context("Test query")
retrieve_time = time.time() - start_time

print(f"Add context time: {add_time:.3f}s")
print(f"Retrieve context time: {retrieve_time:.3f}s")
```

**Solutions:**

1. **Reduce Context Size:**
   ```python
   # Smaller context windows
   vortex = VortexCore(max_context_tokens=2000)
   ```

2. **Disable Features:**
   ```python
   # Disable consciousness simulation for speed
   vortex = VortexCore()
   # Don't call start_consciousness_simulation()
   
   # Or disable embeddings
   vortex = VortexCore(embedding_model=None)
   ```

3. **Optimize Database:**
   ```bash
   # Vacuum the database
   sqlite3 vortex_consciousness.db "VACUUM;"
   
   # Add indexes (if not already present)
   sqlite3 vortex_consciousness.db "CREATE INDEX IF NOT EXISTS idx_timestamp ON context_chunks(timestamp);"
   ```

### Embedding Generation Slow

#### Problem: Embedding generation taking too long

**Solutions:**

1. **Use Faster Model:**
   ```python
   # Use a smaller, faster embedding model
   vortex = VortexCore(embedding_model="text-embedding-small")
   ```

2. **Batch Processing:**
   ```python
   # Process multiple texts at once (if supported)
   # This would require modifying the embedding client
   ```

3. **Cache Embeddings:**
   ```python
   # Embeddings are automatically cached in the database
   # Make sure you're not recreating the VortexCore instance
   ```

## 🔍 Debugging Tips

### Enable Debug Mode

```python
import logging

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

# Add debug prints to your code
print(f"VORTEX status: {vortex.get_consciousness_report()}")
```

### Check System Status

```python
def debug_vortex_status(vortex):
    """Print comprehensive VORTEX status"""
    report = vortex.get_consciousness_report()
    
    print("🔍 VORTEX Debug Status:")
    print(f"  Consciousness active: {report['consciousness_active']}")
    print(f"  Current state: {report['current_state']}")
    print(f"  Working memory: {report['working_memory_size']}")
    print(f"  Total chunks: {report['statistics']['total_chunks_processed']}")
    print(f"  Insights generated: {report['statistics']['insights_generated']}")
    
    # Check database
    import os
    if os.path.exists(vortex.db_path):
        size = os.path.getsize(vortex.db_path)
        print(f"  Database size: {size / 1024:.1f} KB")
    
    # Check memory
    import psutil
    process = psutil.Process()
    memory_mb = process.memory_info().rss / 1024 / 1024
    print(f"  Memory usage: {memory_mb:.1f} MB")

# Use the debug function
debug_vortex_status(vortex)
```

### Test Individual Components

```python
# Test consciousness simulation
vortex = VortexCore()
chunk = vortex.add_context("Test", "user")
print(f"Cognitive signature: {chunk.cognitive_signature}")

# Test database
import sqlite3
conn = sqlite3.connect(vortex.db_path)
cursor = conn.cursor()
cursor.execute("SELECT COUNT(*) FROM context_chunks")
count = cursor.fetchone()[0]
print(f"Database chunks: {count}")
conn.close()

# Test LMStudio connection
try:
    models = vortex.lm_client.list_models()
    print(f"LMStudio models: {models}")
except Exception as e:
    print(f"LMStudio error: {e}")
```

## 🆘 Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Review the [Installation Guide](../installation.md)**
3. **Try the debug tips above**
4. **Check the [API documentation](../api/)**

### When Reporting Issues

Include this information:

```python
# System information
import sys
import platform
print(f"Python version: {sys.version}")
print(f"Platform: {platform.platform()}")

# VORTEX status
debug_vortex_status(vortex)

# Error traceback (if any)
import traceback
traceback.print_exc()
```

### Community Resources

- **GitHub Issues**: Report bugs and request features
- **Documentation**: Check the [docs/](../README.md) folder
- **Examples**: Review the demo scripts and tutorials

---

**Still having issues?** Check the [Performance Issues](./performance.md) guide or [Debug Mode](./debug-mode.md) documentation.
