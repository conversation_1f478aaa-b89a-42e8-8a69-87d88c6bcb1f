<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Context Window Manager - LMStudio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            height: calc(100vh - 200px);
        }

        .chat-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 85%;
            word-wrap: break-word;
        }

        .message.user {
            background: #4facfe;
            color: white;
            margin-left: auto;
        }

        .message.assistant {
            background: white;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .message-meta {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
        }

        .chat-input button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .chat-input button:hover {
            transform: translateY(-2px);
        }

        .chat-input button:disabled {
            opacity: 0.6;
            transform: none;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .panel h3 {
            margin-bottom: 15px;
            color: #4facfe;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 5px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .status-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4facfe;
        }

        .status-label {
            font-size: 0.9em;
            color: #666;
        }

        .timeline {
            max-height: 200px;
            overflow-y: auto;
        }

        .timeline-item {
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 5px;
            font-size: 0.9em;
            transition: background 0.2s;
        }

        .timeline-item:hover {
            background: #f0f0f0;
        }

        .timeline-item.user {
            border-left: 3px solid #4facfe;
        }

        .timeline-item.assistant {
            border-left: 3px solid #28a745;
        }

        .timeline-item.archived {
            opacity: 0.6;
            background: #f8f9fa;
        }

        .controls {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #4facfe;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            display: none;
        }

        .error.show {
            display: block;
        }

        @media (max-width: 1024px) {
            .main-grid {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr auto;
            }
            
            .sidebar {
                flex-direction: row;
                overflow-x: auto;
            }
            
            .panel {
                min-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Context Window Manager</h1>
            <p>Intelligent Memory for Large Language Models</p>
        </div>

        <div class="main-grid">
            <div class="chat-panel">
                <div class="chat-header">
                    <h2>💬 Smart Conversation</h2>
                    <p>Powered by semantic context optimization</p>
                </div>
                
                <div class="error" id="error-message"></div>
                
                <div class="chat-messages" id="chat-messages">
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>Thinking with context...</p>
                    </div>
                </div>
                
                <div class="chat-input">
                    <input type="text" id="message-input" placeholder="Ask me anything... I'll remember our conversation!" maxlength="1000">
                    <button id="send-button" onclick="sendMessage()">Send</button>
                </div>
            </div>

            <div class="sidebar">
                <div class="panel">
                    <h3>📊 Context Status</h3>
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-value" id="rolling-window">0</div>
                            <div class="status-label">Rolling Window</div>
                        </div>
                        <div class="status-item">
                            <div class="status-value" id="archived-count">0</div>
                            <div class="status-label">Archived</div>
                        </div>
                        <div class="status-item">
                            <div class="status-value" id="total-messages">0</div>
                            <div class="status-label">Total Messages</div>
                        </div>
                        <div class="status-item">
                            <div class="status-value" id="semantic-retrievals">0</div>
                            <div class="status-label">Smart Retrievals</div>
                        </div>
                    </div>
                </div>

                <div class="panel">
                    <h3>📈 Conversation Timeline</h3>
                    <div class="timeline" id="timeline"></div>
                </div>

                <div class="panel">
                    <h3>🔧 Controls</h3>
                    <div class="controls">
                        <button class="btn btn-primary" onclick="exportConversation()">💾 Export</button>
                        <button class="btn btn-secondary" onclick="refreshStatus()">🔄 Refresh</button>
                        <button class="btn btn-danger" onclick="clearConversation()">🗑️ Clear</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isLoading = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            
            // Enter key to send message
            document.getElementById('message-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !isLoading) {
                    sendMessage();
                }
            });
        });

        async function sendMessage() {
            if (isLoading) return;
            
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addMessageToChat(message, 'user');
            input.value = '';
            
            // Show loading
            setLoading(true);
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.error) {
                    showError(data.error);
                } else {
                    // Add assistant response
                    addMessageToChat(data.response, 'assistant', data.metadata);
                    
                    // Update status
                    updateStatus(data.context_status);
                    refreshTimeline();
                }
            } catch (error) {
                showError('Failed to get response: ' + error.message);
            } finally {
                setLoading(false);
            }
        }

        function addMessageToChat(message, role, metadata = null) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            let metaText = '';
            if (metadata) {
                metaText = `<div class="message-meta">
                    Context: ${metadata.context_messages_used || 0} messages | 
                    Time: ${(metadata.processing_time || 0).toFixed(2)}s |
                    Efficiency: ${((metadata.context_efficiency || 0) * 100).toFixed(1)}%
                </div>`;
            }
            
            messageDiv.innerHTML = `
                <div>${message}</div>
                ${metaText}
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function setLoading(loading) {
            isLoading = loading;
            const loadingDiv = document.getElementById('loading');
            const sendButton = document.getElementById('send-button');
            
            if (loading) {
                loadingDiv.classList.add('show');
                sendButton.disabled = true;
            } else {
                loadingDiv.classList.remove('show');
                sendButton.disabled = false;
            }
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.classList.add('show');
            
            setTimeout(() => {
                errorDiv.classList.remove('show');
            }, 5000);
        }

        function updateStatus(status) {
            if (!status) return;
            
            document.getElementById('rolling-window').textContent = status.rolling_window_size || 0;
            document.getElementById('archived-count').textContent = status.archived_chunks || 0;
            document.getElementById('total-messages').textContent = status.total_conversation_length || 0;
            document.getElementById('semantic-retrievals').textContent = status.statistics?.semantic_retrievals || 0;
        }

        async function refreshStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                updateStatus(data.status);
                refreshTimeline();
            } catch (error) {
                console.error('Failed to refresh status:', error);
            }
        }

        async function refreshTimeline() {
            try {
                const response = await fetch('/api/visualization');
                const data = await response.json();
                
                const timeline = document.getElementById('timeline');
                timeline.innerHTML = '';
                
                data.timeline.slice(-10).forEach(item => {
                    const div = document.createElement('div');
                    div.className = `timeline-item ${item.role}${item.is_archived ? ' archived' : ''}`;
                    div.innerHTML = `
                        <strong>${item.role.toUpperCase()}:</strong> ${item.content}
                        <br><small>Importance: ${item.importance.toFixed(2)} | Accessed: ${item.access_count}</small>
                    `;
                    timeline.appendChild(div);
                });
            } catch (error) {
                console.error('Failed to refresh timeline:', error);
            }
        }

        async function exportConversation() {
            try {
                const response = await fetch('/api/export');
                const data = await response.json();
                
                if (data.filename) {
                    alert(`Conversation exported to: ${data.filename}`);
                } else {
                    showError('Failed to export conversation');
                }
            } catch (error) {
                showError('Export failed: ' + error.message);
            }
        }

        async function clearConversation() {
            if (!confirm('Are you sure you want to clear the conversation history?')) {
                return;
            }
            
            try {
                const response = await fetch('/api/clear');
                const data = await response.json();
                
                // Clear chat messages
                document.getElementById('chat-messages').innerHTML = '<div class="loading" id="loading"><div class="spinner"></div><p>Thinking with context...</p></div>';
                
                // Refresh status
                refreshStatus();
                
                alert('Conversation cleared successfully');
            } catch (error) {
                showError('Clear failed: ' + error.message);
            }
        }
    </script>
</body>
</html>