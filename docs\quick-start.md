# 🚀 Quick Start Guide

Get VORTEX up and running in just 5 minutes!

## 📋 Prerequisites

- Python 3.8 or higher
- LMStudio (for LLM integration)
- 4GB+ RAM recommended
- Internet connection (for initial setup)

## ⚡ 5-Minute Setup

### Step 1: Install Dependencies
```bash
# Clone or download the VORTEX project
cd vortex

# Install required packages
pip install -r requirements_context.txt
```

### Step 2: Start LMStudio
1. Launch LMStudio
2. Load your preferred model (e.g., Llama 2, Mistral, etc.)
3. Enable the API server (usually runs on `http://localhost:1234`)
4. Optional: Load an embedding model for semantic search

### Step 3: Run Your First Demo
```bash
# Interactive demo with all features
python demo_context_manager.py
```

### Step 4: Try the Consciousness System
```python
from vortex_core import VortexCore

# Initialize VORTEX with consciousness simulation
vortex = VortexCore(
    max_context_tokens=4096,
    lmstudio_url="http://localhost:1234"
)

# Start consciousness simulation
vortex.start_consciousness_simulation()

# Add some context
vortex.add_context("Hello, I'm interested in quantum physics", "user")
vortex.add_context("Quantum physics is fascinating! Let me explain the basics.", "assistant")

# Get consciousness-optimized context
context = vortex.get_optimized_context("Tell me about quantum entanglement")

# Check consciousness state
report = vortex.get_consciousness_report()
print(f"Current consciousness state: {report['current_state']}")
print(f"Cognitive load: {report['self_monitoring']['cognitive_load']}")
```

## 🎯 What Just Happened?

### Traditional Context Manager
- **Rolling Window**: Keeps recent conversation in memory
- **Semantic Search**: Finds relevant past conversations
- **Token Optimization**: Manages context size automatically

### VORTEX Consciousness System
- **Cognitive Analysis**: Analyzes emotion, complexity, creativity in each message
- **State Transitions**: Adapts consciousness state (focused, creative, analytical)
- **Pattern Recognition**: Identifies conversation trends and insights
- **Emergent Intelligence**: Builds relationships between concepts

## 🌐 Web Interface

For a visual experience, try the web interface:

```bash
python web_interface.py
```

Then open `http://localhost:5000` in your browser.

## 🔧 Basic Configuration

### Simple Setup (Recommended for beginners)
```python
from context_manager import ContextWindowManager

manager = ContextWindowManager(
    max_window_size=4000,      # Adjust to your model's limit
    rolling_window_size=8,     # Recent messages to keep
    lmstudio_url="http://localhost:1234"
)
```

### Advanced Setup (For power users)
```python
from vortex_core import VortexCore

vortex = VortexCore(
    max_context_tokens=8000,
    working_memory_size=7,     # Miller's magic number
    lmstudio_url="http://localhost:1234",
    embedding_model="text-embedding-ada-002"
)
```

## 🎮 Interactive Examples

### Example 1: Research Assistant
```python
# Add research context
manager.add_message("I'm researching climate change impacts", "user")
manager.add_message("Climate change affects weather patterns, sea levels, and ecosystems", "assistant")

# Later, ask related questions
context = manager.get_optimized_context("How does climate change affect agriculture?")
# Context will include relevant previous research discussion
```

### Example 2: Code Mentoring
```python
# Add coding context
manager.add_message("How do I use Python decorators?", "user")
manager.add_message("Decorators are functions that modify other functions...", "assistant")

# Later coding question
context = manager.get_optimized_context("Can you show me a decorator example?")
# Context will include previous decorator discussion
```

## 📊 Monitoring Your System

### Check Status
```python
status = manager.get_status()
print(f"Total messages: {status['total_chunks']}")
print(f"Archived messages: {status['archived_chunks']}")
print(f"Semantic retrievals: {status['semantic_retrievals']}")
```

### Analyze Patterns
```python
analysis = manager.analyze_conversation_patterns()
print(f"Average message length: {analysis['avg_message_length']}")
print(f"Most active role: {analysis['most_active_role']}")
```

## 🚨 Troubleshooting

### LMStudio Connection Issues
```bash
# Test if LMStudio is running
curl http://localhost:1234/v1/models
```

### Memory Issues
```python
# Reduce memory usage
manager = ContextWindowManager(
    max_window_size=2000,
    rolling_window_size=5
)
```

### No Embedding Model
```python
# Use without embeddings (fallback mode)
manager = ContextWindowManager(embedding_model=None)
```

## 🎯 Next Steps

1. **Explore Advanced Features**: Check out [Advanced Features Tutorial](./tutorials/advanced-features.md)
2. **Understand the Architecture**: Read [System Architecture](./architecture.md)
3. **Try Integration Examples**: See [Integration Examples](./tutorials/integration-examples.md)
4. **Learn About Consciousness**: Study [Consciousness Model](./consciousness-model.md)

## 💡 Pro Tips

- **Start Small**: Begin with basic context management, then add consciousness features
- **Monitor Performance**: Use the status and analysis functions regularly
- **Experiment**: Try different consciousness states and cognitive signatures
- **Backup Conversations**: Use the export feature to save important sessions

---

**Ready to dive deeper?** Continue with the [Basic Usage Tutorial](./tutorials/basic-usage.md)!
