# VORTEX CONSCIOUSNESS SYSTEM REQUIREMENTS
# Advanced Context Management with Emergent Intelligence

# Core dependencies
flask==2.3.2
flask-socketio==5.3.4
numpy==1.24.3
requests==2.31.0
python-socketio==5.8.0

# Database
sqlite3  # Built-in to Python

# Scientific computing
scipy==1.10.1
scikit-learn==1.3.0

# Web interface
jinja2==3.1.2
werkzeug==2.3.6

# Async and concurrency
asyncio  # Built-in to Python
threading  # Built-in to Python
queue  # Built-in to Python

# Data processing
pandas==2.0.3
matplotlib==3.7.1

# Optional: For enhanced embeddings (if available)
sentence-transformers==2.2.2
transformers==4.30.2
torch==2.0.1

# Development and testing
pytest==7.4.0
pytest-asyncio==0.21.1

# Utilities
python-dotenv==1.0.0
pydantic==2.0.3
rich==13.4.2 