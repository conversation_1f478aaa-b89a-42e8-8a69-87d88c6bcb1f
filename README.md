# 🌀 VORTEX: Consciousness-Aware Context Management System

> *A revolutionary approach to LLM context that creates emergent intelligence patterns through multi-dimensional awareness, cognitive modeling, and self-evolving memory*

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Documentation](https://img.shields.io/badge/docs-comprehensive-green.svg)](./docs/)

## 🧠 What is VORTEX?

VORTEX is an advanced AI context management system that goes beyond traditional token-based approaches. It simulates consciousness-like awareness patterns, cognitive signatures, and emergent intelligence to create more meaningful and contextually rich interactions with Large Language Models.

### 🌟 **Core Innovation: Consciousness Simulation**
- **Cognitive Signatures**: Multi-dimensional analysis of content (emotion, complexity, creativity, logic)
- **Consciousness States**: Dynamic state transitions (focused, creative, analytical, reflective)
- **NeuroCore**: Brain-inspired pattern recognition and insight generation
- **Emergent Intelligence**: Self-evolving memory patterns and relationship mapping

### 🔄 **Advanced Context Management**
- **Rolling Window Management**: Intelligent recent conversation tracking
- **Semantic Memory**: Vector-based similarity matching with embeddings
- **Context Optimization**: Smart token management with importance weighting
- **Persistent Memory**: Long-term conversation archiving and retrieval

### 🚀 **Multi-Modal Integration**
- **LMStudio Integration**: Direct API integration with local models
- **Web Interface**: Real-time visualization and interaction
- **Space Physics Simulation**: Advanced N-body gravitational modeling
- **Consciousness Monitoring**: Real-time awareness state tracking

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements_context.txt
```

### 2. Start LMStudio
- Launch LMStudio with your preferred model
- Enable API server (usually on http://localhost:1234)
- Load an embedding model if available

### 3. Run the Demo
```bash
# Interactive demo with all features
python demo_context_manager.py

# VORTEX consciousness demo
python -c "from vortex_core import VortexCore; v=VortexCore(); v.start_consciousness_simulation(); print('Consciousness active!')"
```

### 4. Basic Usage

#### Traditional Context Management
```python
from context_manager import ContextWindowManager

manager = ContextWindowManager(
    max_window_size=4096,
    rolling_window_size=10,
    lmstudio_url="http://localhost:1234"
)

manager.add_message("Hello, can you help me with Python?", "user")
context = manager.get_optimized_context("Show me decorators")
```

#### VORTEX Consciousness System
```python
from vortex_core import VortexCore

# Initialize with consciousness simulation
vortex = VortexCore(max_context_tokens=4096)
vortex.start_consciousness_simulation()

# Add context with cognitive analysis
chunk = vortex.add_context("I'm interested in quantum physics", "user")
print(f"Cognitive signature: {chunk.cognitive_signature}")

# Get consciousness-aware context
context = vortex.get_optimized_context("Tell me about quantum entanglement")

# Monitor consciousness state
report = vortex.get_consciousness_report()
print(f"Current state: {report['current_state']}")
```

## 🏗️ Architecture

VORTEX features a multi-layered architecture combining traditional context management with consciousness simulation:

### VORTEX Ecosystem
```
┌─────────────────────────────────────────────────────────────────┐
│                        VORTEX ECOSYSTEM                        │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   User Layer    │  Interface Layer │      Integration Layer     │
│                 │                 │                             │
│ • CLI Tools     │ • Web Interface │ • LMStudio API             │
│ • Python API    │ • REST API      │ • External LLMs            │
│ • Demos         │ • Visualizations│ • Custom Extensions        │
└─────────────────┴─────────────────┴─────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                      CORE SYSTEM LAYER                         │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  VORTEX Core    │ Context Manager │     Physics Engine          │
│                 │                 │                             │
│ • NeuroCore     │ • Rolling Window│ • N-Body Simulation         │
│ • Consciousness │ • Semantic Index│ • Gravitational Fields      │
│ • Cognitive Sig │ • Token Mgmt    │ • Relativistic Effects      │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

### Consciousness Model
- **NeuroCore**: Brain-inspired pattern recognition and insight generation
- **Cognitive Signatures**: Multi-dimensional content analysis (emotion, complexity, creativity, etc.)
- **Consciousness States**: Dynamic processing modes (focused, creative, analytical, reflective)
- **Emergent Intelligence**: Self-evolving memory patterns and relationship mapping

## 📊 Advanced Features

### Consciousness Monitoring
```python
# Real-time consciousness state tracking
report = vortex.get_consciousness_report()
print(f"Current state: {report['current_state']}")
print(f"Cognitive load: {report['self_monitoring']['cognitive_load']}")
print(f"Insights generated: {report['statistics']['insights_generated']}")
```

### Cognitive Analysis
```python
# Multi-dimensional content analysis
chunk = vortex.add_context("This is a complex theoretical framework", "user")
sig = chunk.cognitive_signature
print(f"Complexity: {sig.complexity_level:.3f}")
print(f"Abstraction: {sig.abstraction_level:.3f}")
print(f"Creativity: {sig.creativity_index:.3f}")
```

### Pattern Recognition & Insights
```python
# Automatic insight generation from conversation patterns
insights = vortex.neuro_core.generate_insights(conversation_chunks)
for insight in insights:
    print(f"{insight['type']}: {insight['message']}")
```

### Semantic Search & Memory
```python
# Advanced semantic search across all stored content
results = manager.search_semantic("machine learning algorithms", limit=5)
for chunk, similarity in results:
    print(f"Similarity: {similarity:.3f} - {chunk.content[:50]}...")
```

## 🎛️ Configuration Options

### Context Manager Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| `max_window_size` | 8000 | Maximum tokens in context window |
| `rolling_window_size` | 10 | Number of recent messages to keep |
| `importance_threshold` | 0.8 | Minimum importance for archiving |
| `lmstudio_url` | `http://localhost:1234` | LMStudio API endpoint |
| `embedding_model` | Auto-detect | Embedding model name |

### Example Configurations

**For Code Assistance:**
```python
manager = ContextWindowManager(
    max_window_size=6000,    # Longer context for code
    rolling_window_size=15,  # Keep more recent context
    importance_threshold=0.7 # Archive more content
)
```

**For General Chat:**
```python
manager = ContextWindowManager(
    max_window_size=4000,    # Standard context
    rolling_window_size=8,   # Normal window
    importance_threshold=0.8 # Quality over quantity
)
```

## 🔧 Integration Examples

### With Existing Chatbots
```python
from context_manager import ContextWindowManager

class EnhancedChatbot:
    def __init__(self):
        self.context_manager = ContextWindowManager()
    
    def chat(self, user_input):
        # Add user message
        self.context_manager.add_message(user_input, "user")
        
        # Get optimized context
        context = self.context_manager.get_optimized_context(user_input)
        
        # Send to LMStudio
        response = self.generate_response(context)
        
        # Store response
        self.context_manager.add_message(response, "assistant")
        
        return response
```

### With Web Applications
```python
from flask import Flask, request, jsonify
from context_manager import ContextWindowManager

app = Flask(__name__)
manager = ContextWindowManager()

@app.route('/chat', methods=['POST'])
def chat():
    user_message = request.json['message']
    manager.add_message(user_message, "user")
    
    context = manager.get_optimized_context(user_message)
    # Process with your LLM...
    
    return jsonify({"response": response, "context_used": len(context)})
```

## 📈 Performance Optimization

### Memory Management
- **Automatic archiving** of old conversations
- **Compression** of similar content
- **Cleanup** of unused embeddings

### Speed Optimization
- **Cached embeddings** for repeated content
- **Batch processing** for multiple queries
- **Lazy loading** of archived content

### Token Efficiency
- **Smart truncation** algorithms
- **Relevance scoring** for content selection
- **Dynamic window sizing** based on content

## 🛠️ Troubleshooting

### Common Issues

**LMStudio Connection Failed:**
```python
# Check if LMStudio is running
curl http://localhost:1234/v1/models

# Verify API is enabled in LMStudio settings
```

**Embedding Model Not Found:**
```python
# Use fallback pseudo-embeddings
manager = ContextWindowManager(embedding_model=None)
```

**Memory Usage Too High:**
```python
# Reduce window sizes
manager = ContextWindowManager(
    max_window_size=2000,
    rolling_window_size=5
)
```

## 📚 Documentation

### 📖 Complete Documentation Available in `/docs/`

- **[Quick Start Guide](./docs/quick-start.md)** - Get up and running in 5 minutes
- **[Installation Guide](./docs/installation.md)** - Detailed setup instructions
- **[Configuration Guide](./docs/configuration.md)** - System configuration options
- **[System Architecture](./docs/architecture.md)** - High-level system design
- **[Consciousness Model](./docs/consciousness-model.md)** - The brain behind VORTEX

### 🔧 API Reference
- **[VORTEX Core API](./docs/api/vortex-core.md)** - Main consciousness system
- **[Context Manager API](./docs/api/context-manager.md)** - Traditional context management
- **[LMStudio Integration API](./docs/api/lmstudio-integration.md)** - LLM integration

### 📝 Tutorials & Examples
- **[Basic Usage Tutorial](./docs/tutorials/basic-usage.md)** - Step-by-step learning
- **[Advanced Features Tutorial](./docs/tutorials/advanced-features.md)** - Power user features
- **[Integration Examples](./docs/tutorials/integration-examples.md)** - Real-world integrations

### 🧪 Scientific Background
- **[Consciousness Theory](./docs/science/consciousness-theory.md)** - Theoretical foundation
- **[Cognitive Signatures](./docs/science/cognitive-signatures.md)** - Multi-dimensional analysis
- **[Physics Simulation](./docs/science/physics-simulation.md)** - N-body gravitational modeling

### 🔍 Troubleshooting
- **[Common Issues](./docs/troubleshooting/common-issues.md)** - Quick solutions
- **[Performance Issues](./docs/troubleshooting/performance.md)** - Optimization tips
- **[Debug Mode](./docs/troubleshooting/debug-mode.md)** - Advanced debugging

## 🔮 Future Enhancements

### Consciousness Evolution
- [ ] **Enhanced Cognitive Models** with attention mechanisms
- [ ] **Neuromorphic Implementation** using spiking neural networks
- [ ] **Quantum Consciousness Models** with coherence and entanglement
- [ ] **Multi-Agent Consciousness** for collaborative intelligence

### Advanced Features
- [ ] **Hierarchical Memory** (short/medium/long term)
- [ ] **Emotional Intelligence** with advanced emotional processing
- [ ] **Graph-based Memory** for complex concept relationships
- [ ] **Learning from Feedback** loops and adaptation

## 📚 Examples & Use Cases

### 1. **Research Assistant**
- Maintains context across multiple research sessions
- Links related concepts automatically
- Summarizes previous findings

### 2. **Code Mentor**
- Remembers your coding style and preferences
- Builds on previous explanations
- Tracks learning progress

### 3. **Creative Writing**
- Maintains character consistency
- References plot points from earlier
- Suggests based on writing style

### 4. **Technical Support**
- Remembers system configurations
- Builds troubleshooting history
- Provides contextual solutions

## 🤝 Contributing

We'd love your contributions! Areas where help is needed:

- **Consciousness Model Enhancement** - Improve cognitive analysis algorithms
- **Performance Optimization** - Speed and memory improvements
- **Additional LLM Integrations** - Support for more models and APIs
- **Web Interface Improvements** - Enhanced visualizations and interactions
- **Documentation and Examples** - More tutorials and use cases
- **Testing and Bug Reports** - Help us improve reliability

See [Contributing Guide](./docs/development/contributing.md) for details.

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- **LMStudio Team** for the excellent local LLM platform
- **Cognitive Science Community** for consciousness research insights
- **Open-source AI/ML Community** for foundational tools and libraries
- **Contributors and Testers** for feedback and improvements

## 🚀 Get Started Now!

Ready to experience consciousness-aware context management?

```bash
# Quick start
git clone https://github.com/your-username/vortex.git
cd vortex
pip install -r requirements_context.txt
python demo_context_manager.py

# Or try VORTEX consciousness
python -c "
from vortex_core import VortexCore
v = VortexCore()
v.start_consciousness_simulation()
print('🧠 VORTEX consciousness is now active!')
"
```

**Explore the full documentation at [docs/](./docs/README.md)** 📚

---

*VORTEX: Where artificial intelligence meets consciousness simulation* 🌀