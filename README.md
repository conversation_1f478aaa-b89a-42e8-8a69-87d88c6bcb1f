# 🧠 Advanced Context Window Manager for LMStudio

An intelligent context management system that elevates LLM conversations through semantic memory, rolling windows, and adaptive context optimization.

## ✨ Features

### 🔄 **Rolling Window Management**
- Maintains recent conversation context automatically
- Configurable window size based on your model's limits
- Intelligent archiving of older content

### 🔍 **Semantic Memory**
- Uses embeddings to find relevant past conversations
- SQLite-based persistent storage
- Cosine similarity matching for context retrieval

### 🎯 **Context Optimization**
- Smart token management to stay within limits
- Importance weighting for content prioritization
- Automatic context trimming and relevance scoring

### 💾 **Persistent Memory**
- Conversation archiving and retrieval
- Export/import conversation history
- Statistical analysis of conversation patterns

### 🚀 **LMStudio Integration**
- Direct API integration with your local LMStudio instance
- Support for both chat and embedding models
- Real-time context optimization

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements_context.txt
```

### 2. Start LMStudio
- Launch LMStudio with your preferred model
- Enable API server (usually on http://localhost:1234)
- Load an embedding model if available

### 3. Run the Demo
```bash
# Interactive demo with all features
python demo_context_manager.py

# Real-world integration example
python lmstudio_integration.py
```

### 4. Basic Usage

```python
from context_manager import ContextWindowManager

# Initialize the context manager
manager = ContextWindowManager(
    max_window_size=4096,  # Adjust to your model's context limit
    rolling_window_size=10,
    lmstudio_url="http://localhost:1234"
)

# Add messages
manager.add_message("Hello, can you help me with Python?", "user")
manager.add_message("Of course! I'd be happy to help with Python.", "assistant")

# Get optimized context for new query
query = "Show me how to use decorators"
context = manager.get_optimized_context(query)

# The context now includes:
# - Recent conversation (rolling window)
# - Semantically relevant past messages
# - Optimized for token limits
```

## 🏗️ Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────┐
│                Context Window Manager                   │
├─────────────────┬─────────────────┬───────────────────┤
│ Rolling Window  │ Semantic Index  │ LMStudio Client   │
│                 │                 │                   │
│ • Recent msgs   │ • Embeddings    │ • Chat API        │
│ • Auto-archive  │ • Similarity    │ • Embeddings API  │
│ • Importance    │ • SQLite DB     │ • Model mgmt      │
└─────────────────┴─────────────────┴───────────────────┘
```

### Data Flow

1. **Message Input** → Context Chunk Creation
2. **Embedding Generation** → Semantic Index Storage
3. **Query Processing** → Context Optimization
4. **Response Generation** → Memory Update

## 📊 Advanced Features

### Conversation Analysis
```python
analysis = manager.analyze_conversation_patterns()
print(f"Total messages: {analysis['total_messages']}")
print(f"Most accessed content: {analysis['most_accessed_content']}")
```

### Context Optimization Metrics
```python
status = manager.get_status()
print(f"Context efficiency: {status['statistics']['context_optimizations']}")
print(f"Semantic retrievals: {status['statistics']['semantic_retrievals']}")
```

### Export & Import
```python
# Export conversation
filename = manager.export_conversation()

# Analyze patterns
patterns = manager.analyze_conversation_patterns()
```

## 🎛️ Configuration Options

### Context Manager Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| `max_window_size` | 8000 | Maximum tokens in context window |
| `rolling_window_size` | 10 | Number of recent messages to keep |
| `importance_threshold` | 0.8 | Minimum importance for archiving |
| `lmstudio_url` | `http://localhost:1234` | LMStudio API endpoint |
| `embedding_model` | Auto-detect | Embedding model name |

### Example Configurations

**For Code Assistance:**
```python
manager = ContextWindowManager(
    max_window_size=6000,    # Longer context for code
    rolling_window_size=15,  # Keep more recent context
    importance_threshold=0.7 # Archive more content
)
```

**For General Chat:**
```python
manager = ContextWindowManager(
    max_window_size=4000,    # Standard context
    rolling_window_size=8,   # Normal window
    importance_threshold=0.8 # Quality over quantity
)
```

## 🔧 Integration Examples

### With Existing Chatbots
```python
from context_manager import ContextWindowManager

class EnhancedChatbot:
    def __init__(self):
        self.context_manager = ContextWindowManager()
    
    def chat(self, user_input):
        # Add user message
        self.context_manager.add_message(user_input, "user")
        
        # Get optimized context
        context = self.context_manager.get_optimized_context(user_input)
        
        # Send to LMStudio
        response = self.generate_response(context)
        
        # Store response
        self.context_manager.add_message(response, "assistant")
        
        return response
```

### With Web Applications
```python
from flask import Flask, request, jsonify
from context_manager import ContextWindowManager

app = Flask(__name__)
manager = ContextWindowManager()

@app.route('/chat', methods=['POST'])
def chat():
    user_message = request.json['message']
    manager.add_message(user_message, "user")
    
    context = manager.get_optimized_context(user_message)
    # Process with your LLM...
    
    return jsonify({"response": response, "context_used": len(context)})
```

## 📈 Performance Optimization

### Memory Management
- **Automatic archiving** of old conversations
- **Compression** of similar content
- **Cleanup** of unused embeddings

### Speed Optimization
- **Cached embeddings** for repeated content
- **Batch processing** for multiple queries
- **Lazy loading** of archived content

### Token Efficiency
- **Smart truncation** algorithms
- **Relevance scoring** for content selection
- **Dynamic window sizing** based on content

## 🛠️ Troubleshooting

### Common Issues

**LMStudio Connection Failed:**
```python
# Check if LMStudio is running
curl http://localhost:1234/v1/models

# Verify API is enabled in LMStudio settings
```

**Embedding Model Not Found:**
```python
# Use fallback pseudo-embeddings
manager = ContextWindowManager(embedding_model=None)
```

**Memory Usage Too High:**
```python
# Reduce window sizes
manager = ContextWindowManager(
    max_window_size=2000,
    rolling_window_size=5
)
```

## 🔮 Future Enhancements

### Planned Features
- [ ] **Multi-model support** (OpenAI, Anthropic, etc.)
- [ ] **Graph-based memory** for concept relationships
- [ ] **Attention mechanism** for importance weighting
- [ ] **Web interface** for visual context management
- [ ] **Plugin system** for custom extensions
- [ ] **Collaborative conversations** with multiple users

### Advanced Concepts
- [ ] **Hierarchical memory** (short/medium/long term)
- [ ] **Emotional context** tracking
- [ ] **Topic clustering** for better organization
- [ ] **Personality consistency** across conversations
- [ ] **Learning from feedback** loops

## 📚 Examples & Use Cases

### 1. **Research Assistant**
- Maintains context across multiple research sessions
- Links related concepts automatically
- Summarizes previous findings

### 2. **Code Mentor**
- Remembers your coding style and preferences
- Builds on previous explanations
- Tracks learning progress

### 3. **Creative Writing**
- Maintains character consistency
- References plot points from earlier
- Suggests based on writing style

### 4. **Technical Support**
- Remembers system configurations
- Builds troubleshooting history
- Provides contextual solutions

## 🤝 Contributing

We'd love your contributions! Areas where help is needed:

- **Performance optimization**
- **Additional embedding models**
- **Web interface improvements**
- **Documentation and examples**
- **Testing and bug reports**

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- LMStudio team for the excellent local LLM platform
- The open-source AI/ML community
- Contributors and testers

---

**Ready to elevate your LLM conversations? Start with the demo and see the magic happen!** 🚀

```bash
python demo_context_manager.py