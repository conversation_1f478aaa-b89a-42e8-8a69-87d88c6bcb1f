"""
VORTEX WEB INTERFACE
Advanced web interface for the Vortex consciousness-aware context management system
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import asdict
import threading
from pathlib import Path

from flask import Flask, render_template, request, jsonify, session
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
import requests

from vortex_core import VortexCore, ConsciousnessState
from vortex_agents import AgentOrchestrator, ResearchAgent, AnalysisAgent, CreativeAgent, AgentType


class VortexWebServer:
    """Web server for Vortex consciousness system"""
    
    def __init__(self, 
                 host: str = "localhost",
                 port: int = 5000,
                 lmstudio_url: str = "http://localhost:1234",
                 debug: bool = True):
        
        self.host = host
        self.port = port
        self.lmstudio_url = lmstudio_url
        self.debug = debug
        
        # Initialize Flask app
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.secret_key = 'vortex_consciousness_key_2024'
        
        # Initialize SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Initialize Vortex system
        self.vortex_core = VortexCore(lmstudio_url=lmstudio_url)
        self.orchestrator = AgentOrchestrator(self.vortex_core)
        
        # Initialize agents
        self._initialize_agents()
        
        # Setup routes
        self._setup_routes()
        self._setup_socketio_events()
        
        # Session management
        self.active_sessions = {}
        
        # Statistics
        self.server_stats = {
            'total_requests': 0,
            'active_sessions': 0,
            'total_context_processed': 0,
            'consciousness_insights': 0
        }
    
    def _initialize_agents(self):
        """Initialize the agent ecosystem"""
        # Create specialized agents
        researcher = ResearchAgent("researcher_01", self.vortex_core)
        analyst = AnalysisAgent("analyst_01", self.vortex_core)
        creative = CreativeAgent("creative_01", self.vortex_core)
        
        # Add to orchestrator
        self.orchestrator.add_agent(researcher)
        self.orchestrator.add_agent(analyst)
        self.orchestrator.add_agent(creative)
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main dashboard"""
            return self.get_dashboard_html()
        
        @self.app.route('/api/consciousness/report')
        def consciousness_report():
            """Get consciousness state report"""
            self.server_stats['total_requests'] += 1
            report = self.vortex_core.get_consciousness_report()
            return jsonify(report)
        
        @self.app.route('/api/agents/status')
        def agents_status():
            """Get agent status"""
            self.server_stats['total_requests'] += 1
            status = self.orchestrator.get_system_status()
            return jsonify(status)
        
        @self.app.route('/api/context/add', methods=['POST'])
        def add_context():
            """Add context to the system"""
            self.server_stats['total_requests'] += 1
            data = request.json
            
            content = data.get('content', '')
            role = data.get('role', 'user')
            
            if not content:
                return jsonify({'error': 'Content is required'}), 400
            
            # Add to Vortex
            chunk = self.vortex_core.add_context(content, role)
            self.server_stats['total_context_processed'] += 1
            
            return jsonify({
                'success': True,
                'chunk_id': chunk.id,
                'cognitive_signature': asdict(chunk.cognitive_signature) if chunk.cognitive_signature else None
            })
        
        @self.app.route('/api/task/submit', methods=['POST'])
        def submit_task():
            """Submit task to agent orchestrator"""
            self.server_stats['total_requests'] += 1
            data = request.json
            
            description = data.get('description', '')
            agent_type_str = data.get('agent_type', 'researcher')
            priority = data.get('priority', 1)
            context = data.get('context', {})
            
            if not description:
                return jsonify({'error': 'Description is required'}), 400
            
            try:
                agent_type = AgentType(agent_type_str)
                result = self.orchestrator.submit_task(description, agent_type, priority, context)
                return jsonify(result)
            except ValueError:
                return jsonify({'error': f'Invalid agent type: {agent_type_str}'}), 400
        
        @self.app.route('/api/chat', methods=['POST'])
        def chat():
            """Chat with LMStudio through Vortex context management"""
            self.server_stats['total_requests'] += 1
            data = request.json
            
            message = data.get('message', '')
            if not message:
                return jsonify({'error': 'Message is required'}), 400
            
            # Add user message to context
            user_chunk = self.vortex_core.add_context(message, 'user')
            
            # Get optimized context
            optimized_context = self.vortex_core.get_optimized_context(message)
            
            # Make request to LMStudio
            try:
                response = requests.post(
                    f"{self.lmstudio_url}/v1/chat/completions",
                    json={
                        "messages": optimized_context,
                        "temperature": 0.7,
                        "max_tokens": 1000
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    assistant_response = response_data["choices"][0]["message"]["content"]
                    
                    # Add assistant response to context
                    assistant_chunk = self.vortex_core.add_context(assistant_response, 'assistant')
                    
                    return jsonify({
                        'success': True,
                        'response': assistant_response,
                        'user_chunk_id': user_chunk.id,
                        'assistant_chunk_id': assistant_chunk.id,
                        'context_messages_used': len(optimized_context),
                        'consciousness_state': self.vortex_core.neuro_core.consciousness_state.value
                    })
                else:
                    return jsonify({'error': f'LMStudio error: {response.status_code}'}), 500
                    
            except Exception as e:
                return jsonify({'error': f'Failed to connect to LMStudio: {str(e)}'}), 500
        
        @self.app.route('/api/stats')
        def get_stats():
            """Get server statistics"""
            return jsonify({
                'server_stats': self.server_stats,
                'consciousness_report': self.vortex_core.get_consciousness_report(),
                'agent_stats': self.orchestrator.get_system_status()
            })
    
    def _setup_socketio_events(self):
        """Setup SocketIO events for real-time updates"""
        
        @self.socketio.on('connect')
        def handle_connect():
            session_id = request.sid
            self.active_sessions[session_id] = {
                'connected_at': time.time(),
                'last_activity': time.time()
            }
            self.server_stats['active_sessions'] = len(self.active_sessions)
            
            emit('connected', {
                'session_id': session_id,
                'server_time': time.time(),
                'consciousness_state': self.vortex_core.neuro_core.consciousness_state.value
            })
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            session_id = request.sid
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            self.server_stats['active_sessions'] = len(self.active_sessions)
        
        @self.socketio.on('subscribe_consciousness')
        def handle_consciousness_subscription():
            join_room('consciousness_updates')
            emit('subscribed', {'room': 'consciousness_updates'})
        
        @self.socketio.on('subscribe_agents')
        def handle_agents_subscription():
            join_room('agent_updates')
            emit('subscribed', {'room': 'agent_updates'})
        
        @self.socketio.on('realtime_chat')
        def handle_realtime_chat(data):
            """Handle real-time chat through WebSocket"""
            message = data.get('message', '')
            if not message:
                emit('error', {'message': 'Message is required'})
                return
            
            session_id = request.sid
            if session_id in self.active_sessions:
                self.active_sessions[session_id]['last_activity'] = time.time()
            
            # Process through Vortex
            user_chunk = self.vortex_core.add_context(message, 'user')
            
            # Emit user message confirmation
            emit('message_processed', {
                'chunk_id': user_chunk.id,
                'role': 'user',
                'content': message,
                'cognitive_signature': asdict(user_chunk.cognitive_signature) if user_chunk.cognitive_signature else None
            })
            
            # Get LMStudio response (simplified for demo)
            try:
                optimized_context = self.vortex_core.get_optimized_context(message)
                
                # Simulate response (in real implementation, call LMStudio)
                assistant_response = f"Vortex consciousness response to: {message}"
                assistant_chunk = self.vortex_core.add_context(assistant_response, 'assistant')
                
                emit('message_response', {
                    'chunk_id': assistant_chunk.id,
                    'role': 'assistant',
                    'content': assistant_response,
                    'consciousness_state': self.vortex_core.neuro_core.consciousness_state.value,
                    'context_efficiency': len(optimized_context) / len(self.vortex_core.working_memory) if self.vortex_core.working_memory else 1.0
                })
                
            except Exception as e:
                emit('error', {'message': f'Processing error: {str(e)}'})
    
    def broadcast_consciousness_update(self):
        """Broadcast consciousness state updates to subscribed clients"""
        if hasattr(self, 'socketio'):
            consciousness_report = self.vortex_core.get_consciousness_report()
            self.socketio.emit('consciousness_update', consciousness_report, room='consciousness_updates')
    
    def broadcast_agent_update(self):
        """Broadcast agent status updates"""
        if hasattr(self, 'socketio'):
            agent_status = self.orchestrator.get_system_status()
            self.socketio.emit('agent_update', agent_status, room='agent_updates')
    
    def start_background_monitoring(self):
        """Start background monitoring and broadcasting"""
        def monitoring_loop():
            while True:
                time.sleep(5)  # Update every 5 seconds
                self.broadcast_consciousness_update()
                self.broadcast_agent_update()
        
        monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitoring_thread.start()
    
    def get_dashboard_html(self):
        """Generate the dashboard HTML"""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vortex: Consciousness-Aware Context Management</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #fff;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 10px;
        }
        
        .consciousness-state {
            font-size: 1.5em;
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            margin: 10px 0;
        }
        
        .agent-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .agent-active {
            background: rgba(46, 213, 115, 0.3);
        }
        
        .agent-idle {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .chat-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: rgba(0, 0, 0, 0.2);
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        
        .message.user {
            background: rgba(52, 152, 219, 0.3);
            text-align: right;
        }
        
        .message.assistant {
            background: rgba(46, 213, 115, 0.3);
        }
        
        .chat-input {
            display: flex;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .chat-input input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: rgba(46, 213, 115, 0.8);
            color: white;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: rgba(46, 213, 115, 1);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #46d573;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 VORTEX</h1>
            <p>Consciousness-Aware Context Management System</p>
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3>🧠 Consciousness State</h3>
                <div id="consciousness-state" class="consciousness-state pulse">
                    INITIALIZING...
                </div>
                <div class="stats">
                    <div class="stat-item">
                        <div id="working-memory" class="stat-value">0</div>
                        <div class="stat-label">Working Memory</div>
                    </div>
                    <div class="stat-item">
                        <div id="confidence" class="stat-value">0%</div>
                        <div class="stat-label">Confidence</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>🤖 Agent Status</h3>
                <div id="agent-status">
                    <div class="agent-status agent-idle">
                        <span>Researcher</span>
                        <span>IDLE</span>
                    </div>
                    <div class="agent-status agent-idle">
                        <span>Analyst</span>
                        <span>IDLE</span>
                    </div>
                    <div class="agent-status agent-idle">
                        <span>Creative</span>
                        <span>IDLE</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>📊 System Statistics</h3>
                <div class="stats">
                    <div class="stat-item">
                        <div id="total-chunks" class="stat-value">0</div>
                        <div class="stat-label">Context Chunks</div>
                    </div>
                    <div class="stat-item">
                        <div id="insights-generated" class="stat-value">0</div>
                        <div class="stat-label">Insights</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="chat-container">
            <h3>💬 Vortex Chat Interface</h3>
            <div id="chat-messages" class="chat-messages"></div>
            <div class="chat-input">
                <input type="text" id="message-input" placeholder="Enter your message..." onkeypress="handleKeyPress(event)">
                <button class="btn" onclick="sendMessage()">Send</button>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Connection handlers
        socket.on('connect', function() {
            console.log('Connected to Vortex');
            socket.emit('subscribe_consciousness');
            socket.emit('subscribe_agents');
        });
        
        // Consciousness updates
        socket.on('consciousness_update', function(data) {
            document.getElementById('consciousness-state').textContent = data.current_state.toUpperCase();
            document.getElementById('working-memory').textContent = data.working_memory_size;
            document.getElementById('confidence').textContent = Math.round(data.self_monitoring.confidence_level * 100) + '%';
            document.getElementById('total-chunks').textContent = data.statistics.total_chunks_processed;
            document.getElementById('insights-generated').textContent = data.statistics.insights_generated;
        });
        
        // Agent updates
        socket.on('agent_update', function(data) {
            const agentStatusDiv = document.getElementById('agent-status');
            agentStatusDiv.innerHTML = '';
            
            for (const [agentId, status] of Object.entries(data.agent_statuses)) {
                const agentDiv = document.createElement('div');
                agentDiv.className = 'agent-status ' + (status.state === 'idle' ? 'agent-idle' : 'agent-active');
                agentDiv.innerHTML = `
                    <span>${status.agent_type.charAt(0).toUpperCase() + status.agent_type.slice(1)}</span>
                    <span>${status.state.toUpperCase()}</span>
                `;
                agentStatusDiv.appendChild(agentDiv);
            }
        });
        
        // Message response
        socket.on('message_response', function(data) {
            addMessage('assistant', data.content);
        });
        
        // Chat functions
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (message) {
                addMessage('user', message);
                socket.emit('realtime_chat', { message: message });
                input.value = '';
            }
        }
        
        function addMessage(role, content) {
            const messagesDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + role;
            messageDiv.innerHTML = `<strong>${role.charAt(0).toUpperCase() + role.slice(1)}:</strong> ${content}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // Load initial data
        window.onload = function() {
            fetch('/api/consciousness/report')
                .then(response => response.json())
                .then(data => {
                    socket.emit('consciousness_update', data);
                });
                
            fetch('/api/agents/status')
                .then(response => response.json())
                .then(data => {
                    socket.emit('agent_update', data);
                });
        };
    </script>
</body>
</html>
        """
    
    def run(self):
        """Run the web server"""
        print(f"🧠 Vortex Consciousness System starting...")
        print(f"🌐 Web interface: http://{self.host}:{self.port}")
        print(f"🤖 LMStudio URL: {self.lmstudio_url}")
        print(f"🔬 Agents initialized: {len(self.orchestrator.agents)}")
        
        # Start background monitoring
        self.start_background_monitoring()
        
        # Run the server
        self.socketio.run(
            self.app,
            host=self.host,
            port=self.port,
            debug=self.debug
        )


def create_html_template():
    """Create the HTML template for the Vortex dashboard"""
    template_dir = Path("templates")
    template_dir.mkdir(exist_ok=True)
    
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vortex: Consciousness-Aware Context Management</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 3em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card h3 { margin-bottom: 15px; color: #fff; border-bottom: 2px solid rgba(255, 255, 255, 0.3); padding-bottom: 10px; }
        .consciousness-state {
            font-size: 1.5em;
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            margin: 10px 0;
        }
        .agent-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        .agent-active {
            background: rgba(46, 213, 115, 0.3);
        }
        .agent-idle {
            background: rgba(255, 255, 255, 0.1);
        }
        .chat-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: rgba(0, 0, 0, 0.2);
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        .message.user {
            background: rgba(52, 152, 219, 0.3);
            text-align: right;
        }
        .message.assistant {
            background: rgba(46, 213, 115, 0.3);
        }
        .chat-input {
            display: flex;
            gap: 10px;
        }
        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .chat-input input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: rgba(46, 213, 115, 0.8);
            color: white;
            cursor: pointer;
        }
        .pulse { animation: pulse 2s infinite; }
        @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.5; } 100% { opacity: 1; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 VORTEX</h1>
            <p>Consciousness-Aware Context Management System</p>
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3>🧠 Consciousness State</h3>
                <div id="consciousness-state" class="consciousness-state pulse">
                    INITIALIZING...
                </div>
                <div class="stats">
                    <div class="stat-item">
                        <div id="working-memory" class="stat-value">0</div>
                        <div class="stat-label">Working Memory</div>
                    </div>
                    <div class="stat-item">
                        <div id="confidence" class="stat-value">0%</div>
                        <div class="stat-label">Confidence</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>🤖 Agent Status</h3>
                <div id="agent-status">
                    <div class="agent-status agent-idle">
                        <span>Researcher</span>
                        <span>IDLE</span>
                    </div>
                    <div class="agent-status agent-idle">
                        <span>Analyst</span>
                        <span>IDLE</span>
                    </div>
                    <div class="agent-status agent-idle">
                        <span>Creative</span>
                        <span>IDLE</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>📊 System Statistics</h3>
                <div class="stats">
                    <div class="stat-item">
                        <div id="total-chunks" class="stat-value">0</div>
                        <div class="stat-label">Context Chunks</div>
                    </div>
                    <div class="stat-item">
                        <div id="insights-generated" class="stat-value">0</div>
                        <div class="stat-label">Insights</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="chat-container">
            <h3>💬 Vortex Chat Interface</h3>
            <div id="chat-messages" class="chat-messages"></div>
            <div class="chat-input">
                <input type="text" id="message-input" placeholder="Enter your message..." onkeypress="handleKeyPress(event)">
                <button class="btn" onclick="sendMessage()">Send</button>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        
        socket.on('connect', function() {
            console.log('Connected to Vortex');
            socket.emit('subscribe_consciousness');
            socket.emit('subscribe_agents');
        });
        
        socket.on('message_response', function(data) {
            addMessage('assistant', data.content);
            document.getElementById('consciousness-state').textContent = data.consciousness_state.toUpperCase();
        });
        
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (message) {
                addMessage('user', message);
                socket.emit('realtime_chat', { message: message });
                input.value = '';
            }
        }
        
        function addMessage(role, content) {
            const messagesDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + role;
            messageDiv.innerHTML = `<strong>${role.charAt(0).toUpperCase() + role.slice(1)}:</strong> ${content}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // Load initial data
        fetch('/api/consciousness/report')
            .then(response => response.json())
            .then(data => {
                document.getElementById('consciousness-state').textContent = data.current_state.toUpperCase();
            });
            
        fetch('/api/agents/status')
            .then(response => response.json())
            .then(data => {
                const agentDiv = document.getElementById('agent-status');
                agentDiv.innerHTML = `Active Agents: ${data.active_agents}`;
            });
    </script>
</body>
</html>
    """
    
    with open(template_dir / "vortex_dashboard.html", "w", encoding="utf-8") as f:
        f.write(html_content)


if __name__ == "__main__":
    # Create template
    create_html_template()
    
    # Start server
    server = VortexWebServer(
        host="localhost",
        port=5000,
        lmstudio_url="http://localhost:1234",
        debug=True
    )
    
    server.run() 