# 🌀 VORTEX Core API Reference

The VORTEX Core is the heart of the consciousness-aware context management system.

## 📋 Table of Contents

- [VortexCore Class](#vortexcore-class)
- [NeuroCore Class](#neurocore-class)
- [CognitiveSignature Class](#cognitivesignature-class)
- [ContextChunk Class](#contextchunk-class)
- [Enums](#enums)
- [Usage Examples](#usage-examples)

## 🌀 VortexCore Class

The main class that orchestrates consciousness-aware context management.

### Constructor

```python
VortexCore(
    max_context_tokens: int = 8000,
    working_memory_size: int = 7,
    lmstudio_url: str = "http://localhost:1234",
    embedding_model: str = None,
    db_path: str = "vortex_consciousness.db"
)
```

**Parameters:**
- `max_context_tokens`: Maximum tokens in context window
- `working_memory_size`: Size of working memory (default: 7 - <PERSON>'s number)
- `lmstudio_url`: LMStudio API endpoint
- `embedding_model`: Embedding model name (optional)
- `db_path`: SQLite database path for persistent storage

### Core Methods

#### `start_consciousness_simulation()`
Starts the background consciousness simulation thread.

```python
vortex = VortexCore()
vortex.start_consciousness_simulation()
```

#### `add_context(content: str, role: str = "user") -> ContextChunk`
Adds context with full cognitive analysis.

```python
chunk = vortex.add_context(
    content="I'm interested in quantum physics",
    role="user"
)
print(f"Cognitive signature: {chunk.cognitive_signature}")
```

**Returns:** `ContextChunk` with cognitive signature and embedding

#### `get_optimized_context(query: str = None) -> List[Dict[str, str]]`
Gets consciousness-optimized context for the current query.

```python
context = vortex.get_optimized_context("Tell me about quantum entanglement")
# Returns list of {"role": str, "content": str} messages
```

**Features:**
- Combines working memory with semantic search
- Consciousness-state aware selection
- Importance-weighted ranking
- Token optimization

#### `get_consciousness_report() -> Dict[str, Any]`
Gets detailed consciousness state report.

```python
report = vortex.get_consciousness_report()
print(f"Current state: {report['current_state']}")
print(f"Cognitive load: {report['self_monitoring']['cognitive_load']}")
```

**Returns:**
```python
{
    'current_state': str,                    # Current consciousness state
    'self_monitoring': Dict[str, float],     # Metacognitive metrics
    'emotion_state': Dict[str, float],       # Emotional state
    'working_memory_size': int,              # Current working memory size
    'statistics': Dict[str, int],            # System statistics
    'consciousness_active': bool             # Is consciousness thread running
}
```

#### `shutdown()`
Gracefully shuts down the consciousness system.

```python
vortex.shutdown()
```

### Database Methods

#### `init_consciousness_database()`
Initializes the consciousness database with advanced schemas.

**Tables Created:**
- `context_chunks`: Enhanced context storage with cognitive data
- `chunk_relationships`: Relationship mapping between chunks
- `consciousness_log`: Consciousness state and insights logging

### Private Methods

#### `_consciousness_loop()`
Background consciousness simulation loop (runs in separate thread).

#### `_get_embedding(text: str) -> Optional[List[float]]`
Gets embedding for text using LMStudio or fallback pseudo-embedding.

#### `_store_chunk(chunk: ContextChunk)`
Stores chunk in consciousness database with serialized cognitive data.

#### `_find_semantically_relevant(query_embedding: List[float], limit: int = 5) -> List[ContextChunk]`
Finds semantically relevant chunks from episodic memory.

## 🧠 NeuroCore Class

The "brain" of the VORTEX system handling cognitive pattern recognition.

### Constructor

```python
NeuroCore()
```

**Initializes:**
- Consciousness state (default: CONVERSATIONAL)
- Working memory (deque with maxlen=7)
- Pattern weights and concept networks
- Emotion state tracking
- Metacognitive monitoring

### Core Methods

#### `analyze_cognitive_signature(content: str, role: str, context: List[ContextChunk]) -> CognitiveSignature`
Generates a cognitive signature for content.

```python
signature = neuro_core.analyze_cognitive_signature(
    content="This is a complex theoretical framework",
    role="user",
    context=recent_chunks
)
print(f"Complexity: {signature.complexity_level}")
print(f"Abstraction: {signature.abstraction_level}")
```

**Analysis Includes:**
- Emotion valence analysis
- Complexity level calculation
- Abstraction level assessment
- Creativity index evaluation
- Logical coherence scoring
- Temporal urgency detection
- Cognitive load estimation

#### `update_consciousness_state(recent_chunks: List[ContextChunk])`
Updates consciousness state based on recent context.

```python
neuro_core.update_consciousness_state(recent_chunks)
print(f"New state: {neuro_core.consciousness_state}")
```

**State Transition Logic:**
- Creativity > 0.7 → CREATIVE
- Complexity > 0.8 → ANALYTICAL
- |Emotion| > 0.5 → REFLECTIVE
- Complexity > 0.6 + Logic > 0.6 → FOCUSED
- Complexity < 0.3 → EXPLORATORY
- Default → CONVERSATIONAL

#### `generate_insights(chunks: List[ContextChunk]) -> List[Dict]`
Generates insights from context patterns.

```python
insights = neuro_core.generate_insights(conversation_chunks)
for insight in insights:
    print(f"{insight['type']}: {insight['message']}")
    print(f"Confidence: {insight['confidence']}")
```

**Insight Types:**
- `cognitive_evolution`: Complexity/creativity trends
- `pattern_recognition`: Recurring themes
- `emotional_shifts`: Mood changes
- `topic_transitions`: Subject matter evolution

### Properties

#### `consciousness_state: ConsciousnessState`
Current consciousness state.

#### `cognitive_patterns: defaultdict(list)`
Recognized cognitive patterns.

#### `emotion_state: Dict[str, float]`
Current emotional state with valence, arousal, dominance.

#### `self_monitoring: Dict[str, float]`
Metacognitive awareness metrics.

## 🧬 CognitiveSignature Class

Represents the cognitive "fingerprint" of content.

### Constructor

```python
CognitiveSignature(
    emotion_valence: float = 0.0,      # -1 to 1
    complexity_level: float = 0.0,     # 0 to 1
    abstraction_level: float = 0.0,    # 0 to 1
    creativity_index: float = 0.0,     # 0 to 1
    logical_coherence: float = 0.0,    # 0 to 1
    temporal_urgency: float = 0.0,     # 0 to 1
    cognitive_load: float = 0.0        # 0 to 1
)
```

### Methods

#### `similarity(other: 'CognitiveSignature') -> float`
Calculates cognitive similarity between signatures.

```python
sig1 = CognitiveSignature(complexity_level=0.8, creativity_index=0.6)
sig2 = CognitiveSignature(complexity_level=0.7, creativity_index=0.5)
similarity = sig1.similarity(sig2)  # Returns 0.0 to 1.0
```

**Algorithm:**
- Calculates Euclidean distance across all dimensions
- Normalizes to [0,1] range
- Higher values indicate greater similarity

## 📦 ContextChunk Class

Enhanced context chunk with cognitive awareness.

### Constructor

```python
ContextChunk(
    id: str,
    content: str,
    timestamp: float,
    role: str,
    embedding: Optional[List[float]] = None,
    cognitive_signature: Optional[CognitiveSignature] = None,
    importance: float = 1.0,
    access_count: int = 0,
    last_accessed: float = 0.0,
    semantic_tags: Set[str] = field(default_factory=set),
    concepts: Set[str] = field(default_factory=set),
    emotions: Dict[str, float] = field(default_factory=dict),
    related_chunks: Dict[str, float] = field(default_factory=dict),
    causal_relationships: Dict[str, str] = field(default_factory=dict),
    modification_history: List[Dict] = field(default_factory=list),
    synthesis_products: List[str] = field(default_factory=list)
)
```

### Key Properties

- **Core Data**: id, content, timestamp, role
- **AI Enhancement**: embedding, cognitive_signature
- **Usage Tracking**: importance, access_count, last_accessed
- **Semantic Enrichment**: semantic_tags, concepts, emotions
- **Relationship Mapping**: related_chunks, causal_relationships
- **Evolution Tracking**: modification_history, synthesis_products

## 🏷️ Enums

### ConsciousnessState

```python
class ConsciousnessState(Enum):
    FOCUSED = "focused"
    EXPLORATORY = "exploratory"
    REFLECTIVE = "reflective"
    CREATIVE = "creative"
    ANALYTICAL = "analytical"
    CONVERSATIONAL = "conversational"
```

### CognitionType

```python
class CognitionType(Enum):
    ANALYTICAL = "analytical"
    CREATIVE = "creative"
    EMOTIONAL = "emotional"
    LOGICAL = "logical"
    INTUITIVE = "intuitive"
    MEMORY_RECALL = "memory_recall"
    PATTERN_RECOGNITION = "pattern_recognition"
    SYNTHESIS = "synthesis"
    REFLECTION = "reflection"
```

### ContextRelevance

```python
class ContextRelevance(Enum):
    CRITICAL = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    MINIMAL = 1
```

## 💡 Usage Examples

### Basic Setup

```python
from vortex_core import VortexCore

# Initialize VORTEX
vortex = VortexCore(
    max_context_tokens=4096,
    lmstudio_url="http://localhost:1234"
)

# Start consciousness simulation
vortex.start_consciousness_simulation()
```

### Adding Context with Analysis

```python
# Add user message
user_chunk = vortex.add_context(
    "I'm working on a creative writing project about time travel",
    "user"
)

# Check cognitive signature
sig = user_chunk.cognitive_signature
print(f"Creativity: {sig.creativity_index:.2f}")
print(f"Complexity: {sig.complexity_level:.2f}")
print(f"Abstraction: {sig.abstraction_level:.2f}")
```

### Consciousness-Aware Context Retrieval

```python
# Get optimized context for a query
query = "How do paradoxes work in time travel stories?"
context = vortex.get_optimized_context(query)

# Check consciousness state
report = vortex.get_consciousness_report()
print(f"Current state: {report['current_state']}")

# The system will likely be in CREATIVE state due to the creative writing context
```

### Monitoring Insights

```python
import time

# Add several messages and let consciousness process
vortex.add_context("Let's explore quantum mechanics", "user")
vortex.add_context("Quantum mechanics deals with probability and uncertainty", "assistant")
vortex.add_context("How does quantum entanglement work?", "user")

# Wait for consciousness processing
time.sleep(5)

# Check for insights
report = vortex.get_consciousness_report()
if report['consciousness_active']:
    print("Consciousness is actively processing patterns")
```

---

**Next**: Check out the [Context Manager API](./context-manager.md) for traditional context management.
