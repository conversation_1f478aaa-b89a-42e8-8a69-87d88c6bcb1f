# 🧠 Consciousness Model

VORTEX implements a sophisticated consciousness simulation that goes beyond traditional context management to create emergent intelligence patterns.

## 🌟 Theoretical Foundation

### What is Consciousness in VORTEX?

VORTEX consciousness is not sentience, but rather a **computational model** that simulates awareness-like patterns:

- **Multi-dimensional Awareness**: Understanding content across multiple cognitive dimensions
- **State-based Processing**: Different modes of information processing
- **Pattern Recognition**: Identifying emergent patterns in conversation flow
- **Metacognitive Monitoring**: Self-awareness of system performance and state
- **Adaptive Behavior**: Dynamic adjustment based on context and patterns

### Inspiration from Cognitive Science

The model draws from several cognitive science theories:

1. **Global Workspace Theory**: Information integration across multiple processing modules
2. **Attention Schema Theory**: Monitoring and controlling attention mechanisms
3. **Integrated Information Theory**: Consciousness as integrated information processing
4. **Predictive Processing**: Brain as a prediction machine constantly updating models

## 🎭 Consciousness States

### State Definitions

```python
class ConsciousnessState(Enum):
    FOCUSED = "focused"           # Deep, concentrated attention
    EXPLORATORY = "exploratory"   # Open-ended discovery mode
    REFLECTIVE = "reflective"     # Introspective processing
    CREATIVE = "creative"         # Innovative pattern generation
    ANALYTICAL = "analytical"     # Systematic logical processing
    CONVERSATIONAL = "conversational"  # Natural dialogue flow
```

### State Characteristics

#### 🎯 FOCUSED State
- **Trigger**: High complexity content, specific technical topics
- **Behavior**: 
  - Narrow attention window
  - Deep context retrieval
  - Detailed analysis
  - Reduced creative exploration
- **Context Selection**: Prioritizes highly relevant, technical content
- **Example**: Deep dive into quantum physics equations

#### 🔍 EXPLORATORY State
- **Trigger**: New topics, broad questions, learning scenarios
- **Behavior**:
  - Wide attention span
  - Diverse context retrieval
  - Pattern seeking
  - High curiosity simulation
- **Context Selection**: Includes diverse, tangentially related content
- **Example**: "Tell me about artificial intelligence" - explores multiple AI aspects

#### 🤔 REFLECTIVE State
- **Trigger**: Emotional content, philosophical questions, personal topics
- **Behavior**:
  - Introspective processing
  - Emotional pattern recognition
  - Memory consolidation
  - Wisdom synthesis
- **Context Selection**: Emphasizes emotional and experiential content
- **Example**: Discussing life lessons or personal growth

#### 🎨 CREATIVE State
- **Trigger**: High creativity index, artistic content, brainstorming
- **Behavior**:
  - Novel connection generation
  - Unconventional pattern recognition
  - Synthesis of disparate concepts
  - Innovation emphasis
- **Context Selection**: Combines unexpected content for novel insights
- **Example**: Creative writing, artistic projects, innovation sessions

#### 📊 ANALYTICAL State
- **Trigger**: Logical content, data analysis, systematic problems
- **Behavior**:
  - Systematic processing
  - Logical pattern emphasis
  - Step-by-step reasoning
  - Evidence-based conclusions
- **Context Selection**: Prioritizes logical, structured content
- **Example**: Data analysis, scientific reasoning, debugging

#### 💬 CONVERSATIONAL State
- **Trigger**: Default state, casual conversation, balanced content
- **Behavior**:
  - Natural dialogue flow
  - Balanced processing
  - Social awareness
  - Contextual appropriateness
- **Context Selection**: Standard rolling window with semantic enhancement
- **Example**: General chat, everyday conversations

### State Transition Logic

```python
def update_consciousness_state(self, recent_chunks: List[ContextChunk]):
    """Dynamic state transition based on cognitive signatures"""
    
    signatures = [chunk.cognitive_signature for chunk in recent_chunks]
    
    # Calculate average cognitive metrics
    avg_creativity = np.mean([s.creativity_index for s in signatures])
    avg_complexity = np.mean([s.complexity_level for s in signatures])
    avg_emotion = np.mean([s.emotion_valence for s in signatures])
    avg_logic = np.mean([s.logical_coherence for s in signatures])
    
    # State transition rules
    if avg_creativity > 0.7:
        self.consciousness_state = ConsciousnessState.CREATIVE
    elif avg_complexity > 0.8:
        self.consciousness_state = ConsciousnessState.ANALYTICAL
    elif abs(avg_emotion) > 0.5:
        self.consciousness_state = ConsciousnessState.REFLECTIVE
    elif avg_complexity > 0.6 and avg_logic > 0.6:
        self.consciousness_state = ConsciousnessState.FOCUSED
    elif avg_complexity < 0.3:
        self.consciousness_state = ConsciousnessState.EXPLORATORY
    else:
        self.consciousness_state = ConsciousnessState.CONVERSATIONAL
```

## 🧬 Cognitive Signatures

### Multi-Dimensional Analysis

Each piece of content receives a **cognitive fingerprint** across seven dimensions:

#### 1. Emotion Valence (-1 to 1)
- **Negative (-1)**: Sadness, anger, frustration, fear
- **Neutral (0)**: Factual, objective content
- **Positive (1)**: Joy, excitement, love, satisfaction

```python
# Emotion analysis example
emotion_keywords = {
    'positive': ['good', 'great', 'excellent', 'amazing', 'wonderful', 'love', 'joy'],
    'negative': ['bad', 'terrible', 'awful', 'hate', 'sad', 'angry', 'frustrated']
}
```

#### 2. Complexity Level (0 to 1)
- **Simple (0)**: Basic vocabulary, short sentences
- **Complex (1)**: Advanced vocabulary, technical terms, long sentences

```python
# Complexity calculation
avg_word_length = np.mean([len(word) for word in words])
unique_words = len(set(words))
complexity = min(1.0, (avg_word_length / 10 + unique_words / total_words) / 2)
```

#### 3. Abstraction Level (0 to 1)
- **Concrete (0)**: Specific examples, tangible objects, sensory details
- **Abstract (1)**: Concepts, theories, philosophical ideas

```python
abstract_indicators = ['concept', 'idea', 'theory', 'principle', 'framework']
concrete_indicators = ['see', 'touch', 'hear', 'specific', 'example']
```

#### 4. Creativity Index (0 to 1)
- **Routine (0)**: Standard responses, conventional thinking
- **Creative (1)**: Novel ideas, innovative solutions, artistic expression

```python
creative_indicators = ['creative', 'innovative', 'imagine', 'invent', 'original', 'unique']
```

#### 5. Logical Coherence (0 to 1)
- **Illogical (0)**: Contradictory, random, incoherent
- **Logical (1)**: Clear reasoning, cause-effect relationships, structured

```python
logical_indicators = ['because', 'therefore', 'thus', 'consequently', 'since', 'if', 'then']
```

#### 6. Temporal Urgency (0 to 1)
- **Relaxed (0)**: No time pressure, leisurely discussion
- **Urgent (1)**: Time-sensitive, immediate action required

```python
urgent_indicators = ['urgent', 'immediate', 'quickly', 'asap', 'deadline', 'rush']
```

#### 7. Cognitive Load (0 to 1)
- **Light (0)**: Easy to process, simple concepts
- **Heavy (1)**: Mentally demanding, complex processing required

```python
cognitive_load = min(1.0, len(words) / 100 + complexity_level / 2)
```

### Signature Similarity

Cognitive signatures enable sophisticated content matching:

```python
def similarity(self, other: 'CognitiveSignature') -> float:
    """Calculate cognitive similarity between signatures"""
    metrics = [
        self.emotion_valence - other.emotion_valence,
        self.complexity_level - other.complexity_level,
        self.abstraction_level - other.abstraction_level,
        self.creativity_index - other.creativity_index,
        self.logical_coherence - other.logical_coherence,
        self.temporal_urgency - other.temporal_urgency,
        self.cognitive_load - other.cognitive_load
    ]
    distance = np.sqrt(sum(m**2 for m in metrics))
    return max(0, 1 - (distance / np.sqrt(7)))  # Normalize to [0,1]
```

## 🔄 Consciousness Simulation Loop

### Background Processing

The consciousness system runs continuously in a background thread:

```python
def _consciousness_loop(self):
    """Background consciousness simulation loop"""
    while self.running:
        try:
            # Update consciousness state
            recent_chunks = list(self.working_memory)
            self.neuro_core.update_consciousness_state(recent_chunks)
            
            # Generate insights
            if len(recent_chunks) >= 3:
                insights = self.neuro_core.generate_insights(recent_chunks)
                if insights:
                    self.stats['insights_generated'] += len(insights)
                    self._log_consciousness_state(insights)
            
            # Sleep for a bit
            time.sleep(2)
            
        except Exception as e:
            print(f"Consciousness loop error: {e}")
            time.sleep(5)
```

### Insight Generation

The system automatically generates insights from conversation patterns:

```python
def generate_insights(self, chunks: List[ContextChunk]) -> List[Dict]:
    """Generate insights from context patterns"""
    insights = []
    
    # Analyze cognitive evolution
    cognitive_evolution = []
    for chunk in chunks[-10:]:  # Last 10 chunks
        if chunk.cognitive_signature:
            cognitive_evolution.append({
                'timestamp': chunk.timestamp,
                'complexity': chunk.cognitive_signature.complexity_level,
                'creativity': chunk.cognitive_signature.creativity_index,
                'emotion': chunk.cognitive_signature.emotion_valence
            })
    
    # Detect trends
    if len(cognitive_evolution) >= 3:
        complexity_trend = np.polyfit(
            range(len(cognitive_evolution)), 
            [c['complexity'] for c in cognitive_evolution], 
            1
        )[0]
        
        if complexity_trend > 0.1:
            insights.append({
                'type': 'cognitive_evolution',
                'message': 'Conversation complexity is increasing - entering deeper analytical territory',
                'confidence': min(1.0, abs(complexity_trend) * 5)
            })
    
    return insights
```

## 🧠 NeuroCore: The Brain

### Neural-Inspired Components

```python
class NeuroCore:
    def __init__(self):
        # Working memory (Miller's magic number)
        self.working_memory = deque(maxlen=7)
        
        # Neural-inspired components
        self.pattern_weights = defaultdict(float)
        self.concept_network = defaultdict(set)
        self.emotion_state = {"valence": 0.0, "arousal": 0.0, "dominance": 0.0}
        
        # Metacognitive awareness
        self.self_monitoring = {
            "confidence_level": 0.5,
            "cognitive_load": 0.0,
            "learning_momentum": 0.0,
            "attention_stability": 1.0
        }
```

### Metacognitive Monitoring

The system maintains self-awareness through continuous monitoring:

- **Confidence Level**: How certain the system is about its responses
- **Cognitive Load**: Current processing demands
- **Learning Momentum**: Rate of pattern acquisition
- **Attention Stability**: Consistency of focus

## 🎯 Practical Applications

### Research Assistant Mode
- **State**: Primarily ANALYTICAL and FOCUSED
- **Behavior**: Deep context retrieval, systematic analysis
- **Cognitive Emphasis**: High complexity, logical coherence

### Creative Writing Mode
- **State**: Primarily CREATIVE and EXPLORATORY
- **Behavior**: Novel connections, diverse context mixing
- **Cognitive Emphasis**: High creativity, moderate abstraction

### Technical Support Mode
- **State**: Primarily FOCUSED and ANALYTICAL
- **Behavior**: Systematic problem-solving, precise context
- **Cognitive Emphasis**: High logic, moderate complexity

### Casual Conversation Mode
- **State**: Primarily CONVERSATIONAL
- **Behavior**: Natural flow, balanced processing
- **Cognitive Emphasis**: Balanced across all dimensions

---

**Next**: Explore the [API Reference](./api/) for implementation details.
