import math
import time
import random

class Vector3D:
    """Simple 3D vector class using only built-in Python"""
    def __init__(self, x=0.0, y=0.0, z=0.0):
        self.x = float(x)
        self.y = float(y)
        self.z = float(z)
    
    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar):
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def __truediv__(self, scalar):
        return Vector3D(self.x / scalar, self.y / scalar, self.z / scalar)
    
    def magnitude(self):
        return math.sqrt(self.x**2 + self.y**2 + self.z**2)
    
    def normalize(self):
        mag = self.magnitude()
        if mag == 0:
            return Vector3D(0, 0, 0)
        return Vector3D(self.x / mag, self.y / mag, self.z / mag)
    
    def dot(self, other):
        return self.x * other.x + self.y * other.y + self.z * other.z
    
    def cross(self, other):
        return Vector3D(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x
        )
    
    def distance_to(self, other):
        return (self - other).magnitude()
    
    def copy(self):
        return Vector3D(self.x, self.y, self.z)
    
    def __str__(self):
        return f"({self.x:.3f}, {self.y:.3f}, {self.z:.3f})"

class PhysicsConstants:
    G = 6.674e-11  # Gravitational constant (scaled for simulation)
    C = 299792458  # Speed of light
    MIN_DISTANCE = 0.1  # Minimum distance to prevent singularities
    TIDAL_FORCE_SCALE = 0.1
    ROCHE_LIMIT_FACTOR = 2.44

class CelestialBody:
    """Advanced celestial body with realistic physics"""
    
    def __init__(self, position=None, velocity=None, mass=1.0, radius=1.0, 
                 body_type='planet', fixed=False, name=None):
        self.position = position or Vector3D()
        self.velocity = velocity or Vector3D()
        self.acceleration = Vector3D()
        self.mass = mass
        self.radius = radius
        self.body_type = body_type
        self.fixed = fixed
        self.name = name or f"{body_type}_{id(self)}"
        
        # Physics properties
        self.density = mass / (4/3 * math.pi * radius**3)
        self.kinetic_energy = 0.0
        self.potential_energy = 0.0
        self.original_radius = radius
        self.tidal_deformation = 0.0
        
        # Trail for orbital path
        self.trail = []
        self.max_trail_length = 1000
        
    def apply_force(self, force):
        """Apply a force to the body"""
        if not self.fixed:
            acceleration = force / self.mass
            self.acceleration = self.acceleration + acceleration
    
    def apply_tidal_force(self, other):
        """Apply tidal forces from another massive body"""
        if self.fixed:
            return
            
        direction = other.position - self.position
        distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
        
        # Tidal force calculation (gradient of gravitational field)
        tidal_strength = (PhysicsConstants.TIDAL_FORCE_SCALE * 
                         other.mass * self.mass / (distance**3))
        tidal_force = direction.normalize() * tidal_strength
        
        # Apply tidal deformation to radius
        deformation = tidal_strength * 0.01
        self.radius = self.original_radius * (1 + deformation * 0.1)
        self.tidal_deformation = deformation
        
        self.apply_force(tidal_force)
    
    def apply_relativistic_corrections(self, other):
        """Apply relativistic corrections for very massive objects"""
        if self.fixed or other.mass < 50:
            return
            
        direction = other.position - self.position
        distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
        speed = self.velocity.magnitude()
        
        # Simple relativistic correction (post-Newtonian approximation)
        if speed < PhysicsConstants.C * 0.1:  # Only for non-relativistic speeds
            beta = speed / PhysicsConstants.C
            gamma = 1 / math.sqrt(1 - beta**2) if beta < 0.9 else 10
            correction = (gamma - 1) * 0.01
            
            relativistic_force = (direction.normalize() * correction * 
                                other.mass * PhysicsConstants.G / distance**2)
            self.apply_force(relativistic_force)
    
    def is_within_roche_limit(self, other):
        """Check if this body is within the Roche limit of another"""
        distance = self.position.distance_to(other.position)
        roche_limit = (PhysicsConstants.ROCHE_LIMIT_FACTOR * other.radius * 
                      (other.mass / self.mass)**(1/3))
        return distance < roche_limit
    
    def update(self, dt):
        """Update position and velocity using Verlet integration"""
        if self.fixed:
            return
            
        # Verlet integration for numerical stability
        new_position = (self.position + self.velocity * dt + 
                       self.acceleration * (0.5 * dt**2))
        
        # Update velocity
        self.velocity = self.velocity + self.acceleration * dt
        self.position = new_position
        
        # Reset acceleration for next time step
        self.acceleration = Vector3D()
        
        # Update trail
        if len(self.trail) == 0 or self.position.distance_to(self.trail[-1]) > 0.1:
            self.trail.append(self.position.copy())
            if len(self.trail) > self.max_trail_length:
                self.trail.pop(0)
        
        # Calculate energies
        self.calculate_energies()
    
    def calculate_energies(self):
        """Calculate kinetic energy of the body"""
        velocity_magnitude = self.velocity.magnitude()
        self.kinetic_energy = 0.5 * self.mass * velocity_magnitude**2
    
    def get_orbital_parameters(self, central_body):
        """Calculate orbital parameters relative to a central body"""
        if not central_body:
            return None
            
        r = self.position - central_body.position
        v = self.velocity - central_body.velocity
        distance = r.magnitude()
        speed = v.magnitude()
        
        # Standard gravitational parameter
        mu = PhysicsConstants.G * (self.mass + central_body.mass)
        
        # Specific orbital energy
        energy = 0.5 * speed**2 - mu / distance
        
        # Semi-major axis
        if energy < 0:
            semi_major_axis = -mu / (2 * energy)
        else:
            semi_major_axis = float('inf')  # Hyperbolic orbit
        
        # Angular momentum vector
        h = r.cross(v)
        angular_momentum = h.magnitude()
        
        # Eccentricity
        if angular_momentum > 0 and energy < 0:
            eccentricity = math.sqrt(1 + 2 * energy * angular_momentum**2 / mu**2)
        else:
            eccentricity = 0
            
        # Orbital period
        if semi_major_axis > 0 and semi_major_axis != float('inf'):
            period = 2 * math.pi * math.sqrt(semi_major_axis**3 / mu)
        else:
            period = float('inf')
            
        return {
            'semi_major_axis': semi_major_axis,
            'eccentricity': eccentricity,
            'period': period,
            'distance': distance,
            'speed': speed,
            'angular_momentum': angular_momentum,
            'energy': energy
        }

class AdvancedPhysicsSystem:
    """Complete N-body physics system with advanced gravitational dynamics"""
    
    def __init__(self):
        self.bodies = []
        self.gravitational_constant = 1.0
        self.time_scale = 1.0
        
        # Advanced physics toggles
        self.enable_tidal_forces = True
        self.enable_relativistic_effects = False
        self.enable_gravitational_waves = False
        self.tidal_force_strength = 1.0
        
        # System properties
        self.total_energy = 0.0
        self.total_momentum = Vector3D()
        self.angular_momentum = Vector3D()
        self.frame_count = 0
        
    def add_body(self, body):
        """Add a celestial body to the system"""
        self.bodies.append(body)
        print(f"Added {body.name} (mass: {body.mass}, radius: {body.radius})")
    
    def remove_body(self, body):
        """Remove a celestial body from the system"""
        if body in self.bodies:
            self.bodies.remove(body)
            print(f"Removed {body.name}")
    
    def clear(self):
        """Clear all bodies from the system"""
        self.bodies.clear()
        print("Cleared all bodies from system")
    
    def calculate_gravitational_forces(self):
        """Calculate advanced gravitational forces between all bodies"""
        # Reset accelerations
        for body in self.bodies:
            body.acceleration = Vector3D()
        
        # Calculate pairwise gravitational forces
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                
                direction = body2.position - body1.position
                distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
                
                # Basic gravitational force with distance softening
                softening_length = 0.1
                softened_distance = math.sqrt(distance**2 + softening_length**2)
                force_magnitude = (PhysicsConstants.G * self.gravitational_constant * 
                                 body1.mass * body2.mass / softened_distance**2)
                
                # Add tidal gradient effects for close encounters
                if distance < (body1.radius + body2.radius) * 5:
                    tidal_factor = 1 + 0.1 * (body1.radius + body2.radius) / distance
                    force_magnitude *= tidal_factor
                
                force_vector = direction.normalize() * force_magnitude
                
                # Apply Newton's third law
                body1.apply_force(force_vector)
                body2.apply_force(force_vector * -1)
                
                # Apply advanced physics effects
                if self.enable_tidal_forces and distance < (body1.radius + body2.radius) * 10:
                    body1.apply_tidal_force(body2)
                    body2.apply_tidal_force(body1)
                
                if self.enable_relativistic_effects:
                    body1.apply_relativistic_corrections(body2)
                    body2.apply_relativistic_corrections(body1)
                
                # Handle Roche limit violations (tidal disruption)
                if (body1.is_within_roche_limit(body2) or 
                    body2.is_within_roche_limit(body1)):
                    self.handle_roche_limit_violation(body1, body2)
        
        # Apply gravitational wave effects
        if self.enable_gravitational_waves:
            self.apply_gravitational_wave_effects()
    
    def handle_roche_limit_violation(self, body1, body2):
        """Handle tidal disruption within the Roche limit"""
        # Determine which body gets disrupted (the smaller one)
        disrupted = body1 if body1.mass < body2.mass else body2
        disruptor = body2 if body1.mass < body2.mass else body1
        
        print(f"Roche limit violation: {disrupted.name} disrupted by {disruptor.name}")
        
        # Add instability to the disrupted body
        instability_force = Vector3D(
            random.uniform(-0.1, 0.1),
            random.uniform(-0.1, 0.1),
            random.uniform(-0.1, 0.1)
        ) * disrupted.mass
        
        disrupted.apply_force(instability_force)
        
        # Tidal stripping (gradual mass and radius loss)
        disrupted.radius *= 0.9995
        disrupted.mass *= 0.9995
    
    def apply_gravitational_wave_effects(self):
        """Apply gravitational wave energy loss for binary systems"""
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                
                distance = body1.position.distance_to(body2.position)
                total_mass = body1.mass + body2.mass
                reduced_mass = (body1.mass * body2.mass) / total_mass
                
                # Gravitational wave energy loss (Peters & Mathews formula)
                if distance < 50 and total_mass > 10:
                    gw_energy_loss = (1e-15 * (reduced_mass * total_mass**2)**2 / 
                                    distance**5)
                    
                    # Apply energy loss as a small inward force
                    direction = (body2.position - body1.position).normalize()
                    gw_force = direction * gw_energy_loss
                    
                    body1.apply_force(gw_force)
                    body2.apply_force(gw_force * -1)
                    
                    if self.frame_count % 1000 == 0:  # Print occasionally
                        print(f"Gravitational wave energy loss between {body1.name} and {body2.name}")
    
    def find_lagrange_points(self, body1, body2):
        """Find L1 and L2 Lagrange points between two massive bodies"""
        if body1.mass < body2.mass * 0.01:
            return []  # Mass ratio too small for stable Lagrange points
            
        separation = body1.position.distance_to(body2.position)
        mass_ratio = body1.mass / body2.mass
        
        lagrange_points = []
        
        # L1 point (between the two bodies)
        l1_distance = separation * (1 - (mass_ratio / 3)**(1/3))
        direction = (body2.position - body1.position).normalize()
        l1_position = body1.position + direction * l1_distance
        lagrange_points.append({'type': 'L1', 'position': l1_position})
        
        # L2 point (beyond the smaller body)
        l2_distance = separation * (1 + (mass_ratio / 3)**(1/3))
        l2_position = body1.position + direction * l2_distance
        lagrange_points.append({'type': 'L2', 'position': l2_position})
        
        return lagrange_points
    
    def update(self, dt):
        """Update the entire physics system"""
        scaled_dt = dt * self.time_scale
        
        # Calculate all gravitational forces
        self.calculate_gravitational_forces()
        
        # Update all bodies
        for body in self.bodies:
            body.update(scaled_dt)
        
        # Calculate system properties
        self.calculate_system_properties()
        self.frame_count += 1
    
    def calculate_system_properties(self):
        """Calculate total system energy, momentum, and angular momentum"""
        # Total kinetic energy
        total_kinetic = sum(body.kinetic_energy for body in self.bodies)
        
        # Total potential energy
        total_potential = 0
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                distance = max(body1.position.distance_to(body2.position),
                             PhysicsConstants.MIN_DISTANCE)
                potential = (-PhysicsConstants.G * self.gravitational_constant * 
                           body1.mass * body2.mass / distance)
                total_potential += potential
        
        self.total_energy = total_kinetic + total_potential
        
        # Total momentum (should be conserved)
        self.total_momentum = Vector3D()
        for body in self.bodies:
            momentum = body.velocity * body.mass
            self.total_momentum = self.total_momentum + momentum
        
        # Total angular momentum (should be conserved)
        center_of_mass = self.get_center_of_mass()
        self.angular_momentum = Vector3D()
        for body in self.bodies:
            r = body.position - center_of_mass
            p = body.velocity * body.mass
            L = r.cross(p)
            self.angular_momentum = self.angular_momentum + L
    
    def get_center_of_mass(self):
        """Calculate center of mass of the entire system"""
        total_mass = sum(body.mass for body in self.bodies)
        if total_mass == 0:
            return Vector3D()
            
        com_position = Vector3D()
        for body in self.bodies:
            weighted_position = body.position * body.mass
            com_position = com_position + weighted_position
        
        return com_position / total_mass
    
    def get_gravitational_potential(self, position):
        """Calculate gravitational potential at a given position"""
        potential = 0
        for body in self.bodies:
            distance = max(position.distance_to(body.position), 
                          PhysicsConstants.MIN_DISTANCE)
            potential -= (PhysicsConstants.G * self.gravitational_constant * 
                        body.mass / distance)
        return potential
    
    def create_stable_orbit(self, central_body, orbiting_body, distance, velocity=None):
        """Create a stable circular orbit around a central body"""
        if velocity is None:
            # Calculate circular orbital velocity
            mu = PhysicsConstants.G * self.gravitational_constant * central_body.mass
            orbital_velocity = math.sqrt(mu / distance)
        else:
            orbital_velocity = velocity
            
        # Position the orbiting body
        orbiting_body.position = central_body.position + Vector3D(distance, 0, 0)
        
        # Set velocity perpendicular to position for circular orbit
        orbiting_body.velocity = central_body.velocity + Vector3D(0, orbital_velocity, 0)
    
    def print_system_status(self):
        """Print current system status"""
        print(f"\n=== System Status (Frame {self.frame_count}) ===")
        print(f"Bodies: {len(self.bodies)}")
        print(f"Total Energy: {self.total_energy:.6e}")
        print(f"Total Momentum: {self.total_momentum}")
        print(f"Angular Momentum: {self.angular_momentum}")
        print(f"Center of Mass: {self.get_center_of_mass()}")
        
        for i, body in enumerate(self.bodies):
            orbital_params = None
            if len(self.bodies) > 1:
                # Get orbital parameters relative to most massive body
                central_body = max(self.bodies, key=lambda b: b.mass)
                if body != central_body:
                    orbital_params = body.get_orbital_parameters(central_body)
            
            print(f"\n{body.name}:")
            print(f"  Position: {body.position}")
            print(f"  Velocity: {body.velocity}")
            print(f"  Mass: {body.mass:.3f}, Radius: {body.radius:.3f}")
            print(f"  Kinetic Energy: {body.kinetic_energy:.6e}")
            
            if orbital_params and orbital_params['semi_major_axis'] != float('inf'):
                print(f"  Semi-major axis: {orbital_params['semi_major_axis']:.3f}")
                print(f"  Eccentricity: {orbital_params['eccentricity']:.3f}")
                print(f"  Period: {orbital_params['period']:.3f}")

def demonstrate_advanced_physics():
    """Demonstrate the advanced gravitational physics simulation"""
    print("=== Advanced Space Physics Simulation ===")
    print("Initializing physics system with advanced gravitational dynamics...\n")
    
    # Create physics system
    physics = AdvancedPhysicsSystem()
    
    # Enable advanced physics
    physics.enable_tidal_forces = True
    physics.enable_relativistic_effects = True
    physics.enable_gravitational_waves = True
    
    print("Creating solar system...")
    
    # Create Sun
    sun = CelestialBody(
        position=Vector3D(0, 0, 0),
        velocity=Vector3D(0, 0, 0),
        mass=100,  # Very massive
        radius=5,
        body_type='sun',
        fixed=True,  # Sun doesn't move
        name='Sun'
    )
    physics.add_body(sun)
    
    # Create Earth
    earth = CelestialBody(
        position=Vector3D(20, 0, 0),
        velocity=Vector3D(0, 0, 7.1),  # Orbital velocity
        mass=1,
        radius=1,
        body_type='planet',
        name='Earth'
    )
    physics.add_body(earth)
    
    # Create Moon (orbiting Earth)
    moon = CelestialBody(
        position=Vector3D(23, 0, 0),
        velocity=Vector3D(0, 0, 8.2),  # Orbital velocity
        mass=0.1,
        radius=0.3,
        body_type='moon',
        name='Moon'
    )
    physics.add_body(moon)
    
    # Create a massive companion star (for binary system effects)
    companion = CelestialBody(
        position=Vector3D(0, 50, 0),
        velocity=Vector3D(-2, 0, 0),
        mass=80,
        radius=4,
        body_type='sun',
        name='Companion_Star'
    )
    physics.add_body(companion)
    
    print("\nStarting simulation...")
    print("Features demonstrated:")
    print("- N-body gravitational interactions")
    print("- Tidal forces and deformation")
    print("- Relativistic corrections")
    print("- Gravitational wave energy loss")
    print("- Roche limit calculations")
    print("- Orbital mechanics")
    print("- Energy and momentum conservation")
    
    # Simulation loop
    dt = 0.01
    total_time = 0
    print_interval = 50  # Print status every 50 frames
    
    try:
        for step in range(1000):  # Run for 1000 time steps
            physics.update(dt)
            total_time += dt
            
            # Print system status periodically
            if step % print_interval == 0:
                physics.print_system_status()
                
                # Find and display Lagrange points
                if len(physics.bodies) >= 2:
                    sorted_bodies = sorted(physics.bodies, key=lambda b: b.mass, reverse=True)
                    lagrange_points = physics.find_lagrange_points(sorted_bodies[0], sorted_bodies[1])
                    if lagrange_points:
                        print(f"\nLagrange points between {sorted_bodies[0].name} and {sorted_bodies[1].name}:")
                        for point in lagrange_points:
                            print(f"  {point['type']}: {point['position']}")
                
                print(f"\nSimulation time: {total_time:.2f}")
                print("-" * 50)
            
            # Small delay to make output readable
            time.sleep(0.01)
            
    except KeyboardInterrupt:
        print("\nSimulation interrupted by user")
    
    print(f"\nSimulation completed! Total time simulated: {total_time:.2f}")
    physics.print_system_status()

if __name__ == "__main__":
    demonstrate_advanced_physics()