"""
Real-world LMStudio Integration Example
Shows how to use the Context Window Manager with actual LMStudio API calls
"""

import json
import time
from typing import List, Dict, Optional
from context_manager import ContextWindowManager


class SmartLMStudioChat:
    """
    Enhanced LMStudio chat client with intelligent context management
    """
    
    def __init__(self, 
                 lmstudio_url: str = "http://localhost:1234",
                 model_name: str = "llama-2-7b-chat",
                 embedding_model: str = "text-embedding-ada-002",
                 max_context_tokens: int = 4096):
        
        self.lmstudio_url = lmstudio_url
        self.model_name = model_name
        self.max_context_tokens = max_context_tokens
        
        # Initialize context manager
        self.context_manager = ContextWindowManager(
            max_window_size=max_context_tokens,
            rolling_window_size=10,
            lmstudio_url=lmstudio_url,
            embedding_model=embedding_model
        )
        
        # Chat configuration
        self.chat_config = {
            "temperature": 0.7,
            "top_p": 0.9,
            "max_tokens": 1000,
            "stream": False
        }
    
    def chat(self, user_message: str, system_prompt: str = None) -> Dict:
        """
        Enhanced chat with intelligent context management
        """
        start_time = time.time()
        
        # Add user message to context manager
        user_chunk = self.context_manager.add_message(user_message, "user")
        
        # Get optimized context
        optimized_context = self.context_manager.get_optimized_context(user_message)
        
        # Build messages for LMStudio
        messages = []
        
        # Add system prompt if provided
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # Add optimized context
        messages.extend(optimized_context)
        
        # Make API call to LMStudio
        try:
            response = self.context_manager.lm_client.chat_completion(
                messages=messages,
                model=self.model_name,
                **self.chat_config
            )
            
            # Add assistant response to context manager
            assistant_chunk = self.context_manager.add_message(response, "assistant")
            
            # Calculate metrics
            processing_time = time.time() - start_time
            context_efficiency = len(optimized_context) / len(self.context_manager.conversation_history) if self.context_manager.conversation_history else 1.0
            
            return {
                "response": response,
                "metadata": {
                    "processing_time": processing_time,
                    "context_messages_used": len(optimized_context),
                    "total_conversation_length": len(self.context_manager.conversation_history),
                    "context_efficiency": context_efficiency,
                    "user_chunk_id": user_chunk.id,
                    "assistant_chunk_id": assistant_chunk.id
                }
            }
            
        except Exception as e:
            return {
                "response": f"Error: {str(e)}",
                "metadata": {
                    "error": True,
                    "processing_time": time.time() - start_time
                }
            }
    
    def multi_turn_conversation(self, conversation_pairs: List[tuple], system_prompt: str = None):
        """
        Process multiple conversation turns with context optimization
        """
        results = []
        
        for i, (user_msg, expected_context) in enumerate(conversation_pairs):
            print(f"\n--- Turn {i+1} ---")
            print(f"User: {user_msg}")
            
            result = self.chat(user_msg, system_prompt if i == 0 else None)
            
            print(f"Assistant: {result['response']}")
            print(f"Context efficiency: {result['metadata'].get('context_efficiency', 0):.2%}")
            print(f"Processing time: {result['metadata'].get('processing_time', 0):.2f}s")
            
            results.append(result)
            
            # Small delay to simulate natural conversation
            time.sleep(1)
        
        return results
    
    def analyze_conversation_quality(self) -> Dict:
        """
        Analyze the quality and efficiency of the conversation
        """
        analysis = self.context_manager.analyze_conversation_patterns()
        status = self.context_manager.get_status()
        
        # Calculate additional metrics
        total_chunks = len(self.context_manager.conversation_history)
        archived_ratio = len(self.context_manager.archived_chunks) / total_chunks if total_chunks > 0 else 0
        
        quality_metrics = {
            "conversation_length": total_chunks,
            "archive_efficiency": archived_ratio,
            "semantic_retrievals": self.context_manager.stats.get("semantic_retrievals", 0),
            "context_optimizations": self.context_manager.stats.get("context_optimizations", 0),
            "memory_usage_mb": self._estimate_memory_usage(),
            "average_response_relevance": self._calculate_relevance_score()
        }
        
        return {
            "conversation_analysis": analysis,
            "system_status": status,
            "quality_metrics": quality_metrics
        }
    
    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage in MB"""
        total_chars = sum(len(chunk.content) for chunk in self.context_manager.conversation_history)
        total_chars += sum(len(chunk.content) for chunk in self.context_manager.archived_chunks.values())
        
        # Rough estimation: 1 char ≈ 1 byte, plus embedding vectors
        embedding_size = len(self.context_manager.conversation_history) * 768 * 4  # 768-dim floats
        total_bytes = total_chars + embedding_size
        
        return total_bytes / (1024 * 1024)  # Convert to MB
    
    def _calculate_relevance_score(self) -> float:
        """Calculate average relevance score based on semantic retrievals"""
        # Simplified relevance calculation
        retrieval_count = self.context_manager.stats.get("semantic_retrievals", 0)
        total_messages = len(self.context_manager.conversation_history)
        
        if total_messages == 0:
            return 0.0
        
        # Higher retrieval rate indicates more relevant context usage
        return min(retrieval_count / total_messages, 1.0)
    
    def export_enhanced_conversation(self, filename: str = None) -> str:
        """Export conversation with enhanced metadata"""
        base_export = self.context_manager.export_conversation(filename)
        
        # Add quality analysis to export
        quality_data = self.analyze_conversation_quality()
        
        # Read existing export and enhance it
        with open(base_export, 'r', encoding='utf-8') as f:
            export_data = json.load(f)
        
        export_data["quality_analysis"] = quality_data
        export_data["chat_configuration"] = self.chat_config
        export_data["model_info"] = {
            "model_name": self.model_name,
            "lmstudio_url": self.lmstudio_url,
            "max_context_tokens": self.max_context_tokens
        }
        
        # Write enhanced export
        with open(base_export, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return base_export


def demo_real_world_usage():
    """
    Demonstrate real-world usage scenarios
    """
    print("🚀 Real-world LMStudio Integration Demo")
    print("=" * 50)
    
    # Initialize smart chat client
    chat_client = SmartLMStudioChat(
        lmstudio_url="http://localhost:1234",
        model_name="llama-2-7b-chat",  # Adjust to your model
        max_context_tokens=4096
    )
    
    # System prompt for the session
    system_prompt = """You are a helpful AI assistant with expertise in technology, science, and programming. 
    Provide detailed, accurate responses while maintaining context from our conversation."""
    
    # Demo conversation scenarios
    scenarios = [
        # Scenario 1: Technical Discussion
        [
            ("Explain quantum computing basics", "technical_intro"),
            ("How does quantum supremacy work?", "builds_on_quantum"),
            ("What are the current limitations?", "continues_quantum_topic"),
            ("Can you compare it to classical computing?", "quantum_comparison")
        ],
        
        # Scenario 2: Multi-topic conversation
        [
            ("Tell me about machine learning algorithms", "ml_intro"),
            ("What about cooking pasta perfectly?", "topic_switch"),
            ("How do neural networks learn?", "back_to_ml"),
            ("Can AI help with cooking recommendations?", "connects_both_topics")
        ]
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 Scenario {i}: Multi-turn Conversation")
        print("-" * 40)
        
        results = chat_client.multi_turn_conversation(scenario, system_prompt)
        
        # Analyze this scenario
        analysis = chat_client.analyze_conversation_quality()
        print(f"\n📊 Scenario {i} Analysis:")
        print(f"  Quality Score: {analysis['quality_metrics']['average_response_relevance']:.2%}")
        print(f"  Memory Usage: {analysis['quality_metrics']['memory_usage_mb']:.2f} MB")
        print(f"  Context Optimizations: {analysis['quality_metrics']['context_optimizations']}")
        
        time.sleep(2)
    
    # Final comprehensive analysis
    print("\n🔍 COMPREHENSIVE ANALYSIS")
    print("-" * 40)
    final_analysis = chat_client.analyze_conversation_quality()
    
    for category, data in final_analysis.items():
        print(f"\n{category.replace('_', ' ').title()}:")
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.3f}")
                else:
                    print(f"  {key}: {value}")
    
    # Export the conversation
    export_file = chat_client.export_enhanced_conversation("lmstudio_demo_conversation.json")
    print(f"\n💾 Enhanced conversation exported to: {export_file}")
    
    print("\n✅ Real-world integration demo completed!")


class ConversationVisualizer:
    """
    Visualize conversation patterns and context efficiency
    """
    
    def __init__(self, context_manager: ContextWindowManager):
        self.context_manager = context_manager
    
    def generate_conversation_timeline(self) -> Dict:
        """Generate timeline data for visualization"""
        timeline_data = []
        
        for chunk in self.context_manager.conversation_history:
            timeline_data.append({
                "timestamp": chunk.timestamp,
                "role": chunk.role,
                "content_length": len(chunk.content),
                "importance": chunk.importance,
                "access_count": chunk.access_count,
                "is_archived": chunk.id in self.context_manager.archived_chunks
            })
        
        return {
            "timeline": timeline_data,
            "statistics": self.context_manager.stats,
            "current_status": self.context_manager.get_status()
        }
    
    def export_visualization_data(self, filename: str = "conversation_viz_data.json"):
        """Export data for external visualization tools"""
        viz_data = self.generate_conversation_timeline()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(viz_data, f, indent=2, ensure_ascii=False)
        
        print(f"📊 Visualization data exported to: {filename}")
        print("Use this data with tools like D3.js, Plotly, or Matplotlib for visualization")
        
        return filename


if __name__ == "__main__":
    # Run the real-world demo
    demo_real_world_usage()
    
    print("\n" + "="*60)
    print("🎯 Integration Complete!")
    print("Your Context Window Manager is ready for LMStudio!")
    print("="*60)