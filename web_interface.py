"""
Web Interface for Context Window Manager
Simple Flask app to visualize and interact with the context management system
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import json
import os
from datetime import datetime
from context_manager import Context<PERSON><PERSON>owManager
from lmstudio_integration import SmartLMStudioChat

app = Flask(__name__)

# Global context manager instance
context_manager = None
smart_chat = None

def initialize_context_manager():
    """Initialize the context manager"""
    global context_manager, smart_chat
    
    context_manager = ContextWindowManager(
        max_window_size=4096,
        rolling_window_size=8,
        lmstudio_url="http://localhost:1234"
    )
    
    smart_chat = SmartLMStudioChat(
        lmstudio_url="http://localhost:1234",
        max_context_tokens=4096
    )

@app.route('/')
def index():
    """Main interface page"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def api_chat():
    """Handle chat messages"""
    global smart_chat
    
    if not smart_chat:
        initialize_context_manager()
    
    data = request.get_json()
    user_message = data.get('message', '')
    
    if not user_message:
        return jsonify({'error': 'No message provided'}), 400
    
    try:
        # Use smart chat for response
        result = smart_chat.chat(user_message)
        
        # Get current context status
        status = smart_chat.context_manager.get_status()
        
        return jsonify({
            'response': result['response'],
            'metadata': result['metadata'],
            'context_status': status
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def api_status():
    """Get current status"""
    global context_manager
    
    if not context_manager:
        initialize_context_manager()
    
    status = context_manager.get_status()
    analysis = context_manager.analyze_conversation_patterns()
    
    return jsonify({
        'status': status,
        'analysis': analysis
    })

@app.route('/api/visualization')
def api_visualization():
    """Get data for visualization"""
    global context_manager
    
    if not context_manager:
        initialize_context_manager()
    
    # Prepare timeline data
    timeline_data = []
    for i, chunk in enumerate(context_manager.conversation_history):
        timeline_data.append({
            'id': chunk.id,
            'index': i,
            'role': chunk.role,
            'content': chunk.content[:100] + '...' if len(chunk.content) > 100 else chunk.content,
            'timestamp': chunk.timestamp,
            'importance': chunk.importance,
            'access_count': chunk.access_count,
            'is_archived': chunk.id in context_manager.archived_chunks,
            'content_length': len(chunk.content)
        })
    
    return jsonify({
        'timeline': timeline_data,
        'stats': context_manager.stats,
        'rolling_window_ids': [chunk.id for chunk in context_manager.rolling_window]
    })

@app.route('/api/export')
def api_export():
    """Export conversation"""
    global context_manager
    
    if not context_manager:
        return jsonify({'error': 'No conversation to export'}), 400
    
    filename = context_manager.export_conversation()
    return jsonify({'filename': filename, 'message': 'Conversation exported successfully'})

@app.route('/api/clear')
def api_clear():
    """Clear conversation history"""
    global context_manager, smart_chat
    
    # Reinitialize to clear history
    initialize_context_manager()
    
    return jsonify({'message': 'Conversation cleared'})

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    # Initialize context manager
    initialize_context_manager()
    
    print("🌐 Starting Context Manager Web Interface")
    print("🔗 Open http://localhost:5000 in your browser")
    print("🚀 Make sure LMStudio is running on http://localhost:1234")
    
    app.run(debug=True, host='0.0.0.0', port=5000)