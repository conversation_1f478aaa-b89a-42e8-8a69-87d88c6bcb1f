// Physics constants and utilities
const PHYSICS = {
    G: 6.674e-11, // Gravitational constant (scaled for simulation)
    SIMULATION_SCALE: 1e-9, // Scale factor for realistic physics
    MIN_DISTANCE: 0.1, // Minimum distance to prevent division by zero
    TRAIL_LENGTH: 1000, // Maximum trail points
    ENERGY_SCALE: 1e-12, // Scale for energy calculations
    C: 299792458, // Speed of light (for relativistic effects)
    TIDAL_FORCE_SCALE: 0.1, // Scale for tidal force effects
    SCHWARZSCHILD_SCALE: 0.001, // Scale for black hole effects
    GRAVITATIONAL_WAVE_SCALE: 1e-15, // Scale for gravitational wave effects
    ROCHE_LIMIT_FACTOR: 2.44 // Roche limit calculation factor
};

// Vector3 utility class for 3D math
class Vector3 {
    constructor(x = 0, y = 0, z = 0) {
        this.x = x;
        this.y = y;
        this.z = z;
    }

    clone() {
        return new Vector3(this.x, this.y, this.z);
    }

    add(v) {
        return new Vector3(this.x + v.x, this.y + v.y, this.z + v.z);
    }

    subtract(v) {
        return new Vector3(this.x - v.x, this.y - v.y, this.z - v.z);
    }

    multiply(scalar) {
        return new Vector3(this.x * scalar, this.y * scalar, this.z * scalar);
    }

    divide(scalar) {
        return new Vector3(this.x / scalar, this.y / scalar, this.z / scalar);
    }

    magnitude() {
        return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
    }

    normalize() {
        const mag = this.magnitude();
        if (mag === 0) return new Vector3(0, 0, 0);
        return this.divide(mag);
    }

    dot(v) {
        return this.x * v.x + this.y * v.y + this.z * v.z;
    }

    cross(v) {
        return new Vector3(
            this.y * v.z - this.z * v.y,
            this.z * v.x - this.x * v.z,
            this.x * v.y - this.y * v.x
        );
    }

    distanceTo(v) {
        return this.subtract(v).magnitude();
    }

    set(x, y, z) {
        this.x = x;
        this.y = y;
        this.z = z;
        return this;
    }
}

// CelestialBody class representing planets, suns, etc.
class CelestialBody {
    constructor(options = {}) {
        this.id = options.id || Math.random().toString(36).substr(2, 9);
        this.position = options.position || new Vector3();
        this.velocity = options.velocity || new Vector3();
        this.acceleration = new Vector3();
        this.mass = options.mass || 1;
        this.radius = options.radius || 1;
        this.color = options.color || '#ffffff';
        this.type = options.type || 'planet'; // 'planet', 'sun', 'asteroid'
        this.trail = [];
        this.maxTrailLength = options.maxTrailLength || PHYSICS.TRAIL_LENGTH;
        this.fixed = options.fixed || false; // If true, body doesn't move
        this.density = this.mass / (4/3 * Math.PI * Math.pow(this.radius, 3));
        
        // Rendering properties
        this.mesh = null;
        this.trailMesh = null;
        this.velocityArrow = null;
        
        // Physics properties
        this.kineticEnergy = 0;
        this.potentialEnergy = 0;
        this.totalEnergy = 0;
        
        // Initialize trail with current position
        this.trail.push(this.position.clone());
    }

    // Apply force to the body
    applyForce(force) {
        if (!this.fixed) {
            const acceleration = force.divide(this.mass);
            this.acceleration = this.acceleration.add(acceleration);
        }
    }

    // Apply tidal forces from another body
    applyTidalForce(other) {
        if (this.fixed) return;
        
        const direction = other.position.subtract(this.position);
        const distance = Math.max(direction.magnitude(), PHYSICS.MIN_DISTANCE);
        
        // Tidal force is proportional to mass difference and inversely proportional to distance cubed
        const tidalStrength = PHYSICS.TIDAL_FORCE_SCALE * other.mass * this.mass / (distance * distance * distance);
        const tidalForce = direction.normalize().multiply(tidalStrength);
        
        // Apply tidal deformation (affects radius slightly)
        const tidalDeformation = tidalStrength * 0.01;
        this.radius += tidalDeformation * 0.1;
        
        this.applyForce(tidalForce);
    }

    // Calculate gravitational field strength at this body's position
    getGravitationalField(bodies) {
        let field = new Vector3();
        bodies.forEach(other => {
            if (other.id !== this.id) {
                const direction = other.position.subtract(this.position);
                const distance = Math.max(direction.magnitude(), PHYSICS.MIN_DISTANCE);
                const fieldStrength = PHYSICS.G * other.mass / (distance * distance);
                field = field.add(direction.normalize().multiply(fieldStrength));
            }
        });
        return field;
    }

    // Check if this body is within the Roche limit of another body
    isWithinRocheLimit(other) {
        const distance = this.position.distanceTo(other.position);
        const rocheLimit = PHYSICS.ROCHE_LIMIT_FACTOR * other.radius * Math.pow(other.mass / this.mass, 1/3);
        return distance < rocheLimit;
    }

    // Apply relativistic corrections for very massive objects
    applyRelativisticCorrections(other) {
        if (this.fixed) return;
        
        const direction = other.position.subtract(this.position);
        const distance = Math.max(direction.magnitude(), PHYSICS.MIN_DISTANCE);
        const speed = this.velocity.magnitude();
        
        // Simple relativistic correction (approximation)
        if (other.mass > 50) { // For very massive objects
            const beta = speed / PHYSICS.C;
            const gamma = 1 / Math.sqrt(1 - beta * beta);
            const correction = (gamma - 1) * 0.01;
            
            // Apply small relativistic correction to acceleration
            const relativisticForce = direction.normalize().multiply(correction * other.mass * PHYSICS.G / (distance * distance));
            this.applyForce(relativisticForce);
        }
    }

    // Update position and velocity using Verlet integration for better stability
    update(deltaTime) {
        if (this.fixed) return;

        // Verlet integration for more stable orbits
        const newPosition = this.position.add(
            this.velocity.multiply(deltaTime).add(
                this.acceleration.multiply(0.5 * deltaTime * deltaTime)
            )
        );

        const newAcceleration = new Vector3(); // This will be calculated by the physics system
        
        // Update velocity using average acceleration
        this.velocity = this.velocity.add(
            this.acceleration.add(newAcceleration).multiply(0.5 * deltaTime)
        );

        this.position = newPosition;
        this.acceleration = newAcceleration;

        // Update trail
        this.updateTrail();
        
        // Calculate energies
        this.calculateEnergies();
    }

    // Update the orbital trail
    updateTrail() {
        const trailInterval = 5; // Add point every 5 updates
        if (this.trail.length === 0 || 
            this.trail.length % trailInterval === 0 ||
            this.position.distanceTo(this.trail[this.trail.length - 1]) > 0.1) {
            
            this.trail.push(this.position.clone());
            
            if (this.trail.length > this.maxTrailLength) {
                this.trail.shift();
            }
        }
    }

    // Calculate kinetic energy
    calculateKineticEnergy() {
        const velocity = this.velocity.magnitude();
        this.kineticEnergy = 0.5 * this.mass * velocity * velocity;
        return this.kineticEnergy;
    }

    // Calculate potential energy (will be set by physics system)
    calculatePotentialEnergy(bodies) {
        this.potentialEnergy = 0;
        bodies.forEach(other => {
            if (other.id !== this.id) {
                const distance = Math.max(
                    this.position.distanceTo(other.position),
                    PHYSICS.MIN_DISTANCE
                );
                this.potentialEnergy -= PHYSICS.G * this.mass * other.mass / distance;
            }
        });
        return this.potentialEnergy;
    }

    // Calculate total energy
    calculateEnergies() {
        this.calculateKineticEnergy();
        this.totalEnergy = this.kineticEnergy + this.potentialEnergy;
    }

    // Get orbital parameters
    getOrbitalParameters(centralBody) {
        if (!centralBody) return null;

        const r = this.position.subtract(centralBody.position);
        const v = this.velocity.subtract(centralBody.velocity);
        const distance = r.magnitude();
        const speed = v.magnitude();

        // Semi-major axis
        const mu = PHYSICS.G * (this.mass + centralBody.mass);
        const energy = 0.5 * speed * speed - mu / distance;
        const semiMajorAxis = -mu / (2 * energy);

        // Eccentricity
        const h = r.cross(v); // Angular momentum vector
        const angularMomentum = h.magnitude();
        const eccentricity = Math.sqrt(1 + 2 * energy * angularMomentum * angularMomentum / (mu * mu));

        // Period
        const period = 2 * Math.PI * Math.sqrt(Math.pow(semiMajorAxis, 3) / mu);

        return {
            semiMajorAxis,
            eccentricity,
            period,
            distance,
            speed,
            angularMomentum
        };
    }

    // Check collision with another body
    checkCollision(other) {
        const distance = this.position.distanceTo(other.position);
        return distance < (this.radius + other.radius);
    }

    // Merge with another body (for collisions)
    mergeWith(other) {
        const totalMass = this.mass + other.mass;
        const newVelocity = this.velocity.multiply(this.mass)
            .add(other.velocity.multiply(other.mass))
            .divide(totalMass);

        this.velocity = newVelocity;
        this.mass = totalMass;
        this.radius = Math.pow(Math.pow(this.radius, 3) + Math.pow(other.radius, 3), 1/3);
        this.density = this.mass / (4/3 * Math.PI * Math.pow(this.radius, 3));
    }

    // Reset trail
    resetTrail() {
        this.trail = [this.position.clone()];
    }

    // Clone the body
    clone() {
        return new CelestialBody({
            position: this.position.clone(),
            velocity: this.velocity.clone(),
            mass: this.mass,
            radius: this.radius,
            color: this.color,
            type: this.type,
            fixed: this.fixed,
            maxTrailLength: this.maxTrailLength
        });
    }
}

// Physics System class
class PhysicsSystem {
    constructor() {
        this.bodies = [];
        this.gravitationalConstant = 1.0;
        this.timeScale = 1.0;
        this.collisionsEnabled = true;
        this.energyConservation = true;
        this.totalSystemEnergy = 0;
        this.frameCount = 0;
    }

    // Add a body to the system
    addBody(body) {
        this.bodies.push(body);
    }

    // Remove a body from the system
    removeBody(bodyId) {
        this.bodies = this.bodies.filter(body => body.id !== bodyId);
    }

    // Clear all bodies
    clear() {
        this.bodies = [];
    }

    // Calculate gravitational forces between all bodies with advanced dynamics
    calculateGravitationalForces() {
        // Reset accelerations
        this.bodies.forEach(body => {
            body.acceleration = new Vector3();
        });

        // Calculate forces between all pairs
        for (let i = 0; i < this.bodies.length; i++) {
            for (let j = i + 1; j < this.bodies.length; j++) {
                const body1 = this.bodies[i];
                const body2 = this.bodies[j];

                const direction = body2.position.subtract(body1.position);
                const distance = Math.max(direction.magnitude(), PHYSICS.MIN_DISTANCE);
                
                // Basic gravitational force
                let force = PHYSICS.G * this.gravitationalConstant * body1.mass * body2.mass / (distance * distance);
                
                // Apply inverse square law with distance softening for close encounters
                const softeningLength = 0.1;
                const softenedDistance = Math.sqrt(distance * distance + softeningLength * softeningLength);
                force = PHYSICS.G * this.gravitationalConstant * body1.mass * body2.mass / (softenedDistance * softenedDistance);
                
                // Add gravitational gradient (tidal) effects for close bodies
                if (distance < (body1.radius + body2.radius) * 5) {
                    const tidalFactor = 1 + 0.1 * (body1.radius + body2.radius) / distance;
                    force *= tidalFactor;
                }
                
                const forceVector = direction.normalize().multiply(force);

                // Apply force to both bodies (Newton's 3rd law)
                body1.applyForce(forceVector);
                body2.applyForce(forceVector.multiply(-1));
                
                // Apply tidal forces for close encounters
                if (distance < (body1.radius + body2.radius) * 10) {
                    body1.applyTidalForce(body2);
                    body2.applyTidalForce(body1);
                }
                
                // Apply relativistic corrections for massive objects
                if (body1.mass > 20 || body2.mass > 20) {
                    body1.applyRelativisticCorrections(body2);
                    body2.applyRelativisticCorrections(body1);
                }
                
                // Check for Roche limit violations
                if (body1.isWithinRocheLimit(body2) || body2.isWithinRocheLimit(body1)) {
                    this.handleRocheLimitViolation(body1, body2);
                }
            }
        }
        
        // Apply gravitational wave energy loss for binary systems
        this.applyGravitationalWaveEffects();
    }

    // Handle Roche limit violations (tidal disruption)
    handleRocheLimitViolation(body1, body2) {
        // Determine which body gets disrupted (smaller one)
        let disrupted, disruptor;
        if (body1.mass < body2.mass) {
            disrupted = body1;
            disruptor = body2;
        } else {
            disrupted = body2;
            disruptor = body1;
        }
        
        // Add some instability to the disrupted body
        const instabilityForce = new Vector3(
            (Math.random() - 0.5) * 0.1,
            (Math.random() - 0.5) * 0.1,
            (Math.random() - 0.5) * 0.1
        );
        disrupted.applyForce(instabilityForce.multiply(disrupted.mass));
        
        // Slightly reduce the disrupted body's radius (tidal stripping)
        disrupted.radius *= 0.999;
    }

    // Apply gravitational wave energy loss effects
    applyGravitationalWaveEffects() {
        for (let i = 0; i < this.bodies.length; i++) {
            for (let j = i + 1; j < this.bodies.length; j++) {
                const body1 = this.bodies[i];
                const body2 = this.bodies[j];
                
                const distance = body1.position.distanceTo(body2.position);
                const totalMass = body1.mass + body2.mass;
                const reducedMass = (body1.mass * body2.mass) / totalMass;
                
                // Gravitational wave energy loss (very small effect)
                if (distance < 50 && totalMass > 10) {
                    const gwEnergyLoss = PHYSICS.GRAVITATIONAL_WAVE_SCALE *
                        Math.pow(reducedMass * totalMass * totalMass, 2) /
                        Math.pow(distance, 5);
                    
                    // Apply energy loss as a small inward force
                    const direction = body2.position.subtract(body1.position).normalize();
                    const gwForce = direction.multiply(gwEnergyLoss);
                    
                    body1.applyForce(gwForce);
                    body2.applyForce(gwForce.multiply(-1));
                }
            }
        }
    }

    // Calculate gravitational potential at a point
    getGravitationalPotential(position) {
        let potential = 0;
        this.bodies.forEach(body => {
            const distance = Math.max(position.distanceTo(body.position), PHYSICS.MIN_DISTANCE);
            potential -= PHYSICS.G * this.gravitationalConstant * body.mass / distance;
        });
        return potential;
    }

    // Find Lagrange points between two massive bodies
    findLagrangePoints(body1, body2) {
        if (body1.mass < body2.mass * 0.01) return []; // Too small mass ratio
        
        const separation = body1.position.distanceTo(body2.position);
        const massRatio = body1.mass / body2.mass;
        const lagrangePoints = [];
        
        // L1 point (between the bodies)
        const l1Distance = separation * (1 - Math.pow(massRatio / 3, 1/3));
        const l1Direction = body2.position.subtract(body1.position).normalize();
        const l1Position = body1.position.add(l1Direction.multiply(l1Distance));
        lagrangePoints.push({ type: 'L1', position: l1Position });
        
        // L2 point (beyond the smaller body)
        const l2Distance = separation * (1 + Math.pow(massRatio / 3, 1/3));
        const l2Position = body1.position.add(l1Direction.multiply(l2Distance));
        lagrangePoints.push({ type: 'L2', position: l2Position });
        
        return lagrangePoints;
    }

    // Update all bodies
    update(deltaTime) {
        const scaledDeltaTime = deltaTime * this.timeScale;
        
        // Calculate gravitational forces
        this.calculateGravitationalForces();

        // Update positions and velocities
        this.bodies.forEach(body => {
            body.update(scaledDeltaTime);
        });

        // Handle collisions
        if (this.collisionsEnabled) {
            this.handleCollisions();
        }

        // Calculate system energy
        this.calculateSystemEnergy();

        this.frameCount++;
    }

    // Handle collisions between bodies
    handleCollisions() {
        const toRemove = [];
        
        for (let i = 0; i < this.bodies.length; i++) {
            for (let j = i + 1; j < this.bodies.length; j++) {
                const body1 = this.bodies[i];
                const body2 = this.bodies[j];

                if (body1.checkCollision(body2)) {
                    // Merge smaller body into larger one
                    if (body1.mass >= body2.mass) {
                        body1.mergeWith(body2);
                        toRemove.push(body2.id);
                    } else {
                        body2.mergeWith(body1);
                        toRemove.push(body1.id);
                    }
                }
            }
        }

        // Remove merged bodies
        toRemove.forEach(id => this.removeBody(id));
    }

    // Calculate total system energy
    calculateSystemEnergy() {
        let totalKinetic = 0;
        let totalPotential = 0;

        this.bodies.forEach(body => {
            totalKinetic += body.calculateKineticEnergy();
            totalPotential += body.calculatePotentialEnergy(this.bodies);
        });

        // Potential energy is double-counted, so divide by 2
        totalPotential /= 2;
        
        this.totalSystemEnergy = totalKinetic + totalPotential;
        
        return {
            kinetic: totalKinetic,
            potential: totalPotential,
            total: this.totalSystemEnergy
        };
    }

    // Get system center of mass
    getCenterOfMass() {
        let totalMass = 0;
        let centerOfMass = new Vector3();

        this.bodies.forEach(body => {
            totalMass += body.mass;
            centerOfMass = centerOfMass.add(body.position.multiply(body.mass));
        });

        if (totalMass > 0) {
            centerOfMass = centerOfMass.divide(totalMass);
        }

        return centerOfMass;
    }

    // Create a stable orbital configuration
    createStableOrbit(centralBody, orbitingBody, distance, velocity) {
        const mu = PHYSICS.G * this.gravitationalConstant * centralBody.mass;
        const orbitalVelocity = velocity || Math.sqrt(mu / distance);
        
        orbitingBody.position = centralBody.position.add(new Vector3(distance, 0, 0));
        orbitingBody.velocity = centralBody.velocity.add(new Vector3(0, orbitalVelocity, 0));
    }

    // Reset all body trails
    resetTrails() {
        this.bodies.forEach(body => body.resetTrail());
    }

    // Get bodies by type
    getBodiesByType(type) {
        return this.bodies.filter(body => body.type === type);
    }

    // Get most massive body (usually the sun)
    getMostMassiveBody() {
        return this.bodies.reduce((max, body) => 
            body.mass > max.mass ? body : max, 
            this.bodies[0] || null
        );
    }
}

// Export for global use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Vector3, CelestialBody, PhysicsSystem, PHYSICS };
}