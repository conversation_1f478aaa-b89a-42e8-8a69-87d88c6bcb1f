// Main simulation class
class SpaceSimulation {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.physicsSystem = new PhysicsSystem();
        
        // Simulation state
        this.isPaused = false;
        this.showOrbits = true;
        this.showVelocityVectors = false;
        this.showGrid = false;
        this.showGravityField = false;
        this.showLagrangePoints = false;
        this.enableTidalForces = true;
        this.enableRelativisticEffects = false;
        this.enableGravitationalWaves = false;
        
        // Rendering objects
        this.bodyMeshes = new Map();
        this.trailMeshes = new Map();
        this.velocityArrows = new Map();
        this.gridHelper = null;
        
        // UI elements
        this.uiElements = {};
        
        // Performance tracking
        this.lastTime = 0;
        this.frameCount = 0;
        this.fpsUpdateTime = 0;
        
        // Body creation parameters
        this.bodyCreationParams = {
            mass: 1.0,
            radius: 1.0,
            initialVelocity: 2.0
        };
        
        // Colors for different body types
        this.bodyColors = {
            sun: 0xFFD700,
            planet: 0x4169E1,
            asteroid: 0x8B4513,
            moon: 0xC0C0C0
        };
        
        this.init();
    }

    // Initialize the simulation
    init() {
        this.initThreeJS();
        this.initPhysics();
        this.initUI();
        this.initControls();
        this.setupEventListeners();
        this.animate();
        
        // Add some default bodies
        this.createDefaultSystem();
    }

    // Initialize Three.js scene, camera, and renderer
    initThreeJS() {
        const canvas = document.getElementById('simulation-canvas');
        const container = document.getElementById('canvas-container');
        
        // Scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000011);
        
        // Camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            container.clientWidth / container.clientHeight,
            0.1,
            10000
        );
        this.camera.position.set(0, 20, 30);
        
        // Renderer
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(container.clientWidth, container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Orbit controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.enableZoom = true;
        this.controls.enablePan = true;
        this.controls.maxDistance = 500;
        this.controls.minDistance = 1;
        
        // Lighting
        this.setupLighting();
        
        // Grid
        this.gridHelper = new THREE.GridHelper(100, 20, 0x444444, 0x222222);
        this.gridHelper.visible = this.showGrid;
        this.scene.add(this.gridHelper);
        
        // Starfield
        this.createStarfield();
        
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    // Setup lighting
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // Point light (sun)
        const sunLight = new THREE.PointLight(0xFFFFFF, 1, 0, 2);
        sunLight.position.set(0, 0, 0);
        sunLight.castShadow = true;
        sunLight.shadow.mapSize.width = 2048;
        sunLight.shadow.mapSize.height = 2048;
        this.scene.add(sunLight);
        
        // Directional light for general illumination
        const directionalLight = new THREE.DirectionalLight(0xFFFFFF, 0.5);
        directionalLight.position.set(50, 50, 50);
        this.scene.add(directionalLight);
    }

    // Create starfield background
    createStarfield() {
        const starsGeometry = new THREE.BufferGeometry();
        const starsMaterial = new THREE.PointsMaterial({
            color: 0xFFFFFF,
            size: 0.5,
            sizeAttenuation: false
        });
        
        const starsVertices = [];
        for (let i = 0; i < 10000; i++) {
            const x = (Math.random() - 0.5) * 2000;
            const y = (Math.random() - 0.5) * 2000;
            const z = (Math.random() - 0.5) * 2000;
            starsVertices.push(x, y, z);
        }
        
        starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
        const starField = new THREE.Points(starsGeometry, starsMaterial);
        this.scene.add(starField);
    }

    // Initialize physics system
    initPhysics() {
        this.physicsSystem = new PhysicsSystem();
    }

    // Initialize UI elements
    initUI() {
        this.uiElements = {
            timeScale: document.getElementById('timeScale'),
            timeScaleValue: document.getElementById('timeScaleValue'),
            gravitationalConstant: document.getElementById('gravitationalConstant'),
            gravitationalConstantValue: document.getElementById('gravitationalConstantValue'),
            pausePlay: document.getElementById('pausePlay'),
            reset: document.getElementById('reset'),
            showOrbits: document.getElementById('showOrbits'),
            showVelocityVectors: document.getElementById('showVelocityVectors'),
            showGrid: document.getElementById('showGrid'),
            showGravityField: document.getElementById('showGravityField'),
            showLagrangePoints: document.getElementById('showLagrangePoints'),
            enableTidalForces: document.getElementById('enableTidalForces'),
            enableRelativisticEffects: document.getElementById('enableRelativisticEffects'),
            enableGravitationalWaves: document.getElementById('enableGravitationalWaves'),
            tidalForceStrength: document.getElementById('tidalForceStrength'),
            tidalForceStrengthValue: document.getElementById('tidalForceStrengthValue'),
            addSun: document.getElementById('addSun'),
            addPlanet: document.getElementById('addPlanet'),
            bodyMass: document.getElementById('bodyMass'),
            bodyMassValue: document.getElementById('bodyMassValue'),
            bodyRadius: document.getElementById('bodyRadius'),
            bodyRadiusValue: document.getElementById('bodyRadiusValue'),
            initialVelocity: document.getElementById('initialVelocity'),
            initialVelocityValue: document.getElementById('initialVelocityValue'),
            bodyCount: document.getElementById('bodyCount'),
            totalEnergy: document.getElementById('totalEnergy'),
            fps: document.getElementById('fps')
        };
    }

    // Initialize controls and update UI
    initControls() {
        // Time scale
        this.uiElements.timeScale.addEventListener('input', (e) => {
            this.physicsSystem.timeScale = parseFloat(e.target.value);
            this.uiElements.timeScaleValue.textContent = `${e.target.value}x`;
        });
        
        // Gravitational constant
        this.uiElements.gravitationalConstant.addEventListener('input', (e) => {
            this.physicsSystem.gravitationalConstant = parseFloat(e.target.value);
            this.uiElements.gravitationalConstantValue.textContent = e.target.value;
        });
        
        // Body mass
        this.uiElements.bodyMass.addEventListener('input', (e) => {
            this.bodyCreationParams.mass = parseFloat(e.target.value);
            this.uiElements.bodyMassValue.textContent = e.target.value;
        });
        
        // Body radius
        this.uiElements.bodyRadius.addEventListener('input', (e) => {
            this.bodyCreationParams.radius = parseFloat(e.target.value);
            this.uiElements.bodyRadiusValue.textContent = e.target.value;
        });
        
        // Initial velocity
        this.uiElements.initialVelocity.addEventListener('input', (e) => {
            this.bodyCreationParams.initialVelocity = parseFloat(e.target.value);
            this.uiElements.initialVelocityValue.textContent = e.target.value;
        });
        
        // Visualization toggles
        this.uiElements.showOrbits.addEventListener('change', (e) => {
            this.showOrbits = e.target.checked;
            this.updateTrailVisibility();
        });
        
        this.uiElements.showVelocityVectors.addEventListener('change', (e) => {
            this.showVelocityVectors = e.target.checked;
            this.updateVelocityArrowVisibility();
        });
        
        this.uiElements.showGrid.addEventListener('change', (e) => {
            this.showGrid = e.target.checked;
            this.gridHelper.visible = this.showGrid;
        });
        
        // Advanced visualization toggles
        this.uiElements.showGravityField.addEventListener('change', (e) => {
            this.showGravityField = e.target.checked;
            this.updateGravityFieldVisibility();
        });
        
        this.uiElements.showLagrangePoints.addEventListener('change', (e) => {
            this.showLagrangePoints = e.target.checked;
            this.updateLagrangePointsVisibility();
        });
        
        // Advanced physics toggles
        this.uiElements.enableTidalForces.addEventListener('change', (e) => {
            this.enableTidalForces = e.target.checked;
            this.physicsSystem.enableTidalForces = this.enableTidalForces;
        });
        
        this.uiElements.enableRelativisticEffects.addEventListener('change', (e) => {
            this.enableRelativisticEffects = e.target.checked;
            this.physicsSystem.enableRelativisticEffects = this.enableRelativisticEffects;
        });
        
        this.uiElements.enableGravitationalWaves.addEventListener('change', (e) => {
            this.enableGravitationalWaves = e.target.checked;
            this.physicsSystem.enableGravitationalWaves = this.enableGravitationalWaves;
        });
        
        // Tidal force strength
        this.uiElements.tidalForceStrength.addEventListener('input', (e) => {
            const strength = parseFloat(e.target.value);
            this.physicsSystem.tidalForceStrength = strength;
            this.uiElements.tidalForceStrengthValue.textContent = strength.toFixed(1);
        });
    }

    // Setup event listeners
    setupEventListeners() {
        // Pause/Play
        this.uiElements.pausePlay.addEventListener('click', () => {
            this.isPaused = !this.isPaused;
            this.uiElements.pausePlay.textContent = this.isPaused ? 'Play' : 'Pause';
        });
        
        // Reset
        this.uiElements.reset.addEventListener('click', () => {
            this.resetSimulation();
        });
        
        // Add Sun
        this.uiElements.addSun.addEventListener('click', () => {
            this.addSun();
        });
        
        // Add Planet
        this.uiElements.addPlanet.addEventListener('click', () => {
            this.addPlanet();
        });
        
        // Mouse interaction for adding bodies
        this.renderer.domElement.addEventListener('dblclick', (event) => {
            if (event.shiftKey) {
                this.addBodyAtMouse(event, 'planet');
            } else if (event.ctrlKey) {
                this.addBodyAtMouse(event, 'sun');
            }
        });
    }

    // Create default solar system
    createDefaultSystem() {
        // Create sun
        const sun = new CelestialBody({
            position: new Vector3(0, 0, 0),
            velocity: new Vector3(0, 0, 0),
            mass: 10,
            radius: 3,
            color: '#FFD700',
            type: 'sun',
            fixed: true
        });
        
        this.physicsSystem.addBody(sun);
        this.createBodyMesh(sun);
        
        // Create a planet
        const planet = new CelestialBody({
            position: new Vector3(15, 0, 0),
            velocity: new Vector3(0, 0, 2.5),
            mass: 1,
            radius: 1,
            color: '#4169E1',
            type: 'planet'
        });
        
        this.physicsSystem.addBody(planet);
        this.createBodyMesh(planet);
        
        this.updateUI();
    }

    // Add sun at random position
    addSun() {
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * 20 + 10;
        
        const sun = new CelestialBody({
            position: new Vector3(
                Math.cos(angle) * distance,
                (Math.random() - 0.5) * 5,
                Math.sin(angle) * distance
            ),
            velocity: new Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ),
            mass: this.bodyCreationParams.mass * 5,
            radius: this.bodyCreationParams.radius * 2,
            color: '#FFD700',
            type: 'sun'
        });
        
        this.physicsSystem.addBody(sun);
        this.createBodyMesh(sun);
        this.updateUI();
    }

    // Add planet with orbital velocity
    addPlanet() {
        const centralBody = this.physicsSystem.getMostMassiveBody();
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * 25 + 8;
        
        const planet = new CelestialBody({
            position: new Vector3(
                Math.cos(angle) * distance,
                (Math.random() - 0.5) * 3,
                Math.sin(angle) * distance
            ),
            mass: this.bodyCreationParams.mass,
            radius: this.bodyCreationParams.radius,
            color: this.getRandomPlanetColor(),
            type: 'planet'
        });
        
        if (centralBody) {
            // Calculate orbital velocity for stable orbit
            const mu = PHYSICS.G * this.physicsSystem.gravitationalConstant * centralBody.mass;
            const orbitalSpeed = Math.sqrt(mu / distance) * this.bodyCreationParams.initialVelocity;
            
            // Perpendicular velocity for orbit
            planet.velocity = new Vector3(
                -Math.sin(angle) * orbitalSpeed,
                (Math.random() - 0.5) * orbitalSpeed * 0.1,
                Math.cos(angle) * orbitalSpeed
            );
        }
        
        this.physicsSystem.addBody(planet);
        this.createBodyMesh(planet);
        this.updateUI();
    }

    // Get random planet color
    getRandomPlanetColor() {
        const colors = ['#4169E1', '#FF6347', '#32CD32', '#FFD700', '#8A2BE2', '#FF69B4'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    // Add body at mouse position
    addBodyAtMouse(event, type) {
        const rect = this.renderer.domElement.getBoundingClientRect();
        const mouse = new THREE.Vector2(
            ((event.clientX - rect.left) / rect.width) * 2 - 1,
            -((event.clientY - rect.top) / rect.height) * 2 + 1
        );
        
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.camera);
        
        // Create a plane at z=0 to intersect with
        const plane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0);
        const intersection = new THREE.Vector3();
        raycaster.ray.intersectPlane(plane, intersection);
        
        const body = new CelestialBody({
            position: new Vector3(intersection.x, intersection.y, intersection.z),
            velocity: new Vector3(
                (Math.random() - 0.5) * 3,
                (Math.random() - 0.5) * 3,
                (Math.random() - 0.5) * 3
            ),
            mass: type === 'sun' ? this.bodyCreationParams.mass * 5 : this.bodyCreationParams.mass,
            radius: type === 'sun' ? this.bodyCreationParams.radius * 2 : this.bodyCreationParams.radius,
            color: type === 'sun' ? '#FFD700' : this.getRandomPlanetColor(),
            type: type
        });
        
        this.physicsSystem.addBody(body);
        this.createBodyMesh(body);
        this.updateUI();
    }

    // Create 3D mesh for a celestial body
    createBodyMesh(body) {
        // Create sphere geometry
        const geometry = new THREE.SphereGeometry(body.radius, 32, 32);
        
        // Create material based on body type
        let material;
        if (body.type === 'sun') {
            material = new THREE.MeshStandardMaterial({
                color: body.color,
                emissive: body.color,
                emissiveIntensity: 0.3,
                roughness: 0.1,
                metalness: 0.1
            });
        } else {
            material = new THREE.MeshStandardMaterial({
                color: body.color,
                roughness: 0.8,
                metalness: 0.2
            });
        }
        
        // Create mesh
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(body.position);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        
        this.scene.add(mesh);
        this.bodyMeshes.set(body.id, mesh);
        
        // Create trail
        this.createTrailMesh(body);
        
        // Create velocity arrow
        this.createVelocityArrow(body);
    }

    // Create trail mesh for orbital paths
    createTrailMesh(body) {
        const trailGeometry = new THREE.BufferGeometry();
        const trailMaterial = new THREE.LineBasicMaterial({
            color: body.color,
            opacity: 0.6,
            transparent: true
        });
        
        const trailMesh = new THREE.Line(trailGeometry, trailMaterial);
        trailMesh.visible = this.showOrbits;
        this.scene.add(trailMesh);
        this.trailMeshes.set(body.id, trailMesh);
    }

    // Create velocity arrow
    createVelocityArrow(body) {
        const arrowGeometry = new THREE.ConeGeometry(0.1, 0.5, 8);
        const arrowMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });
        const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
        
        const lineGeometry = new THREE.BufferGeometry();
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0xFF0000 });
        const line = new THREE.Line(lineGeometry, lineMaterial);
        
        const group = new THREE.Group();
        group.add(arrow);
        group.add(line);
        group.visible = this.showVelocityVectors;
        
        this.scene.add(group);
        this.velocityArrows.set(body.id, group);
    }

    // Update trail visibility
    updateTrailVisibility() {
        this.trailMeshes.forEach(mesh => {
            mesh.visible = this.showOrbits;
        });
    }

    // Update velocity arrow visibility
    updateVelocityArrowVisibility() {
        this.velocityArrows.forEach(arrow => {
            arrow.visible = this.showVelocityVectors;
        });
    }

    // Update gravity field visualization
    updateGravityFieldVisibility() {
        if (this.showGravityField) {
            this.createGravityFieldVisualization();
        } else {
            this.removeGravityFieldVisualization();
        }
    }

    // Create gravity field visualization
    createGravityFieldVisualization() {
        if (this.gravityFieldMesh) {
            this.scene.remove(this.gravityFieldMesh);
        }

        const fieldSize = 50;
        const fieldResolution = 20;
        const step = fieldSize / fieldResolution;
        
        const fieldGeometry = new THREE.BufferGeometry();
        const fieldPositions = [];
        const fieldColors = [];
        
        for (let x = -fieldSize/2; x <= fieldSize/2; x += step) {
            for (let z = -fieldSize/2; z <= fieldSize/2; z += step) {
                const position = new Vector3(x, 0, z);
                const potential = this.physicsSystem.getGravitationalPotential(position);
                const fieldStrength = Math.abs(potential) * 100; // Scale for visibility
                
                if (fieldStrength > 0.01) {
                    fieldPositions.push(x, 0, z);
                    
                    // Color based on field strength (blue to red)
                    const intensity = Math.min(fieldStrength, 1);
                    fieldColors.push(intensity, 0, 1 - intensity);
                }
            }
        }
        
        fieldGeometry.setAttribute('position', new THREE.Float32BufferAttribute(fieldPositions, 3));
        fieldGeometry.setAttribute('color', new THREE.Float32BufferAttribute(fieldColors, 3));
        
        const fieldMaterial = new THREE.PointsMaterial({
            size: 0.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.6
        });
        
        this.gravityFieldMesh = new THREE.Points(fieldGeometry, fieldMaterial);
        this.scene.add(this.gravityFieldMesh);
    }

    // Remove gravity field visualization
    removeGravityFieldVisualization() {
        if (this.gravityFieldMesh) {
            this.scene.remove(this.gravityFieldMesh);
            this.gravityFieldMesh = null;
        }
    }

    // Update Lagrange points visibility
    updateLagrangePointsVisibility() {
        if (this.showLagrangePoints) {
            this.createLagrangePointsVisualization();
        } else {
            this.removeLagrangePointsVisualization();
        }
    }

    // Create Lagrange points visualization
    createLagrangePointsVisualization() {
        this.removeLagrangePointsVisualization();
        this.lagrangePointMeshes = [];
        
        // Find the two most massive bodies
        const sortedBodies = [...this.physicsSystem.bodies].sort((a, b) => b.mass - a.mass);
        if (sortedBodies.length >= 2) {
            const body1 = sortedBodies[0];
            const body2 = sortedBodies[1];
            
            const lagrangePoints = this.physicsSystem.findLagrangePoints(body1, body2);
            
            lagrangePoints.forEach(point => {
                const geometry = new THREE.SphereGeometry(0.3, 8, 8);
                const material = new THREE.MeshBasicMaterial({
                    color: 0x00FF00,
                    transparent: true,
                    opacity: 0.7
                });
                
                const mesh = new THREE.Mesh(geometry, material);
                mesh.position.copy(point.position);
                
                this.scene.add(mesh);
                this.lagrangePointMeshes.push(mesh);
            });
        }
    }

    // Remove Lagrange points visualization
    removeLagrangePointsVisualization() {
        if (this.lagrangePointMeshes) {
            this.lagrangePointMeshes.forEach(mesh => {
                this.scene.remove(mesh);
            });
            this.lagrangePointMeshes = [];
        }
    }

    // Update physics and rendering
    update(deltaTime) {
        if (!this.isPaused) {
            this.physicsSystem.update(deltaTime);
            this.updateVisuals();
        }
        
        this.controls.update();
        this.updateUI();
    }

    // Update visual elements
    updateVisuals() {
        this.physicsSystem.bodies.forEach(body => {
            // Update body mesh position
            const mesh = this.bodyMeshes.get(body.id);
            if (mesh) {
                mesh.position.copy(body.position);
                
                // Add glow effect for suns
                if (body.type === 'sun') {
                    mesh.material.emissiveIntensity = 0.3 + Math.sin(Date.now() * 0.005) * 0.2;
                }
            }
            
            // Update trail
            this.updateTrail(body);
            
            // Update velocity arrow
            this.updateVelocityArrow(body);
        });
    }

    // Update trail for a body
    updateTrail(body) {
        const trailMesh = this.trailMeshes.get(body.id);
        if (!trailMesh || body.trail.length < 2) return;
        
        const positions = [];
        body.trail.forEach(point => {
            positions.push(point.x, point.y, point.z);
        });
        
        trailMesh.geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        trailMesh.geometry.setDrawRange(0, body.trail.length);
    }

    // Update velocity arrow for a body
    updateVelocityArrow(body) {
        const arrow = this.velocityArrows.get(body.id);
        if (!arrow) return;
        
        const velocity = body.velocity;
        const speed = velocity.magnitude();
        
        if (speed > 0.01) {
            const direction = velocity.normalize();
            const position = body.position.clone();
            
            // Position arrow at body surface
            arrow.position.copy(position);
            
            // Scale arrow based on velocity
            const scale = Math.min(speed * 2, 5);
            arrow.scale.set(scale, scale, scale);
            
            // Point arrow in velocity direction
            arrow.lookAt(position.add(direction));
            
            // Update line geometry
            const line = arrow.children[1];
            const linePositions = [0, 0, 0, direction.x * scale, direction.y * scale, direction.z * scale];
            line.geometry.setAttribute('position', new THREE.Float32BufferAttribute(linePositions, 3));
        }
    }

    // Update UI information
    updateUI() {
        this.uiElements.bodyCount.textContent = this.physicsSystem.bodies.length;
        
        const energy = this.physicsSystem.calculateSystemEnergy();
        this.uiElements.totalEnergy.textContent = energy.total.toFixed(2);
        
        // Update FPS
        this.frameCount++;
        const now = performance.now();
        if (now - this.fpsUpdateTime > 1000) {
            const fps = this.frameCount / ((now - this.fpsUpdateTime) / 1000);
            this.uiElements.fps.textContent = Math.round(fps);
            this.frameCount = 0;
            this.fpsUpdateTime = now;
        }
    }

    // Reset simulation
    resetSimulation() {
        // Clear physics
        this.physicsSystem.clear();
        
        // Clear visual elements
        this.bodyMeshes.forEach(mesh => this.scene.remove(mesh));
        this.trailMeshes.forEach(mesh => this.scene.remove(mesh));
        this.velocityArrows.forEach(arrow => this.scene.remove(arrow));
        
        this.bodyMeshes.clear();
        this.trailMeshes.clear();
        this.velocityArrows.clear();
        
        // Recreate default system
        this.createDefaultSystem();
        
        // Reset camera
        this.camera.position.set(0, 20, 30);
        this.controls.reset();
        
        this.updateUI();
    }

    // Handle window resize
    onWindowResize() {
        const container = document.getElementById('canvas-container');
        this.camera.aspect = container.clientWidth / container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(container.clientWidth, container.clientHeight);
    }

    // Main animation loop
    animate() {
        requestAnimationFrame(() => this.animate());
        
        const currentTime = performance.now();
        const deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        this.update(deltaTime);
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize simulation when page loads
document.addEventListener('DOMContentLoaded', () => {
    const simulation = new SpaceSimulation();
    
    // Make simulation globally accessible for debugging
    window.simulation = simulation;
});