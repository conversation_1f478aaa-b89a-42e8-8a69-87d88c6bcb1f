# ⚙️ Configuration Guide

Complete guide to configuring VORTEX for your specific needs and use cases.

## 📋 Configuration Overview

VORTEX offers multiple configuration approaches:
- **Constructor Parameters**: Direct configuration when creating instances
- **Environment Variables**: System-wide configuration
- **Configuration Files**: Persistent configuration management
- **Runtime Configuration**: Dynamic configuration changes

## 🔧 Core Configuration Parameters

### VortexCore Configuration

```python
VortexCore(
    max_context_tokens: int = 8000,
    working_memory_size: int = 7,
    lmstudio_url: str = "http://localhost:1234",
    embedding_model: str = None,
    db_path: str = "vortex_consciousness.db"
)
```

#### `max_context_tokens`
- **Purpose**: Maximum tokens in context window
- **Default**: 8000
- **Range**: 1000-32000 (depends on your LLM)
- **Impact**: Higher values = more context, more memory usage

```python
# For different model sizes
vortex_small = VortexCore(max_context_tokens=2048)   # Small models
vortex_medium = VortexCore(max_context_tokens=4096)  # Medium models
vortex_large = VortexCore(max_context_tokens=8192)   # Large models
vortex_xl = VortexCore(max_context_tokens=16384)     # Extra large models
```

#### `working_memory_size`
- **Purpose**: Size of active working memory (Miller's number)
- **Default**: 7
- **Range**: 3-15
- **Impact**: Higher values = more recent context, more processing

```python
# For different use cases
vortex_focused = VortexCore(working_memory_size=3)   # Focused conversations
vortex_standard = VortexCore(working_memory_size=7)  # Standard conversations
vortex_complex = VortexCore(working_memory_size=12)  # Complex discussions
```

#### `lmstudio_url`
- **Purpose**: LMStudio API endpoint
- **Default**: "http://localhost:1234"
- **Format**: "http://host:port"

```python
# Different LMStudio configurations
vortex_local = VortexCore(lmstudio_url="http://localhost:1234")
vortex_remote = VortexCore(lmstudio_url="http://*************:1234")
vortex_custom = VortexCore(lmstudio_url="http://my-server:8080")
```

#### `embedding_model`
- **Purpose**: Embedding model for semantic search
- **Default**: None (auto-detect)
- **Options**: Model names available in LMStudio

```python
# Embedding model options
vortex_ada = VortexCore(embedding_model="text-embedding-ada-002")
vortex_small = VortexCore(embedding_model="text-embedding-small")
vortex_none = VortexCore(embedding_model=None)  # Disable embeddings
```

#### `db_path`
- **Purpose**: SQLite database file path
- **Default**: "vortex_consciousness.db"
- **Format**: File path string

```python
# Different database configurations
vortex_default = VortexCore(db_path="vortex_consciousness.db")
vortex_memory = VortexCore(db_path=":memory:")  # In-memory database
vortex_custom = VortexCore(db_path="/path/to/my_vortex.db")
```

### ContextWindowManager Configuration

```python
ContextWindowManager(
    max_window_size: int = 8000,
    rolling_window_size: int = 10,
    importance_threshold: float = 0.8,
    lmstudio_url: str = "http://localhost:1234",
    embedding_model: str = None
)
```

#### `rolling_window_size`
- **Purpose**: Number of recent messages to keep in memory
- **Default**: 10
- **Range**: 3-50
- **Impact**: Higher values = more recent context

#### `importance_threshold`
- **Purpose**: Minimum importance for archiving messages
- **Default**: 0.8
- **Range**: 0.0-1.0
- **Impact**: Lower values = more messages archived

## 🌍 Environment Variables

Set system-wide defaults using environment variables:

```bash
# Core settings
export VORTEX_LMSTUDIO_URL="http://localhost:1234"
export VORTEX_MAX_TOKENS="8000"
export VORTEX_WORKING_MEMORY_SIZE="7"
export VORTEX_DB_PATH="./vortex_consciousness.db"
export VORTEX_EMBEDDING_MODEL="text-embedding-ada-002"

# Advanced settings
export VORTEX_CONSCIOUSNESS_ENABLED="true"
export VORTEX_DEBUG_MODE="false"
export VORTEX_LOG_LEVEL="INFO"

# Web interface settings
export VORTEX_WEB_HOST="0.0.0.0"
export VORTEX_WEB_PORT="5000"
export VORTEX_WEB_DEBUG="false"
```

### Using Environment Variables

```python
import os
from vortex_core import VortexCore

# Read from environment variables
vortex = VortexCore(
    max_context_tokens=int(os.getenv('VORTEX_MAX_TOKENS', 8000)),
    working_memory_size=int(os.getenv('VORTEX_WORKING_MEMORY_SIZE', 7)),
    lmstudio_url=os.getenv('VORTEX_LMSTUDIO_URL', 'http://localhost:1234'),
    embedding_model=os.getenv('VORTEX_EMBEDDING_MODEL'),
    db_path=os.getenv('VORTEX_DB_PATH', 'vortex_consciousness.db')
)
```

## 📄 Configuration Files

### JSON Configuration

Create `vortex_config.json`:

```json
{
    "core": {
        "max_context_tokens": 8000,
        "working_memory_size": 7,
        "lmstudio_url": "http://localhost:1234",
        "embedding_model": "text-embedding-ada-002",
        "db_path": "./vortex_consciousness.db"
    },
    "consciousness": {
        "enabled": true,
        "simulation_interval": 2.0,
        "insight_generation": true,
        "pattern_recognition": true
    },
    "context_manager": {
        "max_window_size": 8000,
        "rolling_window_size": 10,
        "importance_threshold": 0.8
    },
    "web_interface": {
        "host": "0.0.0.0",
        "port": 5000,
        "debug": false
    },
    "logging": {
        "level": "INFO",
        "file": "vortex.log",
        "console": true
    }
}
```

### Loading Configuration

```python
import json
from vortex_core import VortexCore

def load_config(config_file="vortex_config.json"):
    """Load configuration from JSON file"""
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print(f"Config file {config_file} not found, using defaults")
        return {}

# Use configuration
config = load_config()
core_config = config.get('core', {})

vortex = VortexCore(
    max_context_tokens=core_config.get('max_context_tokens', 8000),
    working_memory_size=core_config.get('working_memory_size', 7),
    lmstudio_url=core_config.get('lmstudio_url', 'http://localhost:1234'),
    embedding_model=core_config.get('embedding_model'),
    db_path=core_config.get('db_path', 'vortex_consciousness.db')
)
```

### YAML Configuration (Optional)

If you prefer YAML, install PyYAML and create `vortex_config.yaml`:

```yaml
core:
  max_context_tokens: 8000
  working_memory_size: 7
  lmstudio_url: "http://localhost:1234"
  embedding_model: "text-embedding-ada-002"
  db_path: "./vortex_consciousness.db"

consciousness:
  enabled: true
  simulation_interval: 2.0
  insight_generation: true
  pattern_recognition: true

context_manager:
  max_window_size: 8000
  rolling_window_size: 10
  importance_threshold: 0.8

web_interface:
  host: "0.0.0.0"
  port: 5000
  debug: false

logging:
  level: "INFO"
  file: "vortex.log"
  console: true
```

```python
import yaml

def load_yaml_config(config_file="vortex_config.yaml"):
    """Load configuration from YAML file"""
    try:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        return {}
```

## 🎯 Use Case Configurations

### Research Assistant Configuration

```python
# Optimized for research and analysis
research_config = {
    "max_context_tokens": 12000,      # Large context for complex topics
    "working_memory_size": 12,        # More working memory
    "rolling_window_size": 15,        # Keep more recent context
    "importance_threshold": 0.6,      # Archive more content
    "consciousness_enabled": True,    # Use consciousness features
    "preferred_states": ["ANALYTICAL", "FOCUSED"]
}

research_vortex = VortexCore(
    max_context_tokens=research_config["max_context_tokens"],
    working_memory_size=research_config["working_memory_size"]
)
```

### Creative Writing Configuration

```python
# Optimized for creative writing
creative_config = {
    "max_context_tokens": 8000,       # Standard context
    "working_memory_size": 10,        # More creative memory
    "rolling_window_size": 12,        # Keep creative flow
    "importance_threshold": 0.7,      # Moderate archiving
    "consciousness_enabled": True,    # Essential for creativity
    "preferred_states": ["CREATIVE", "EXPLORATORY"]
}

creative_vortex = VortexCore(
    max_context_tokens=creative_config["max_context_tokens"],
    working_memory_size=creative_config["working_memory_size"]
)
```

### Technical Support Configuration

```python
# Optimized for technical support
support_config = {
    "max_context_tokens": 4000,       # Focused context
    "working_memory_size": 5,         # Focused memory
    "rolling_window_size": 6,         # Recent problem context
    "importance_threshold": 0.9,      # Only archive important info
    "consciousness_enabled": True,    # For problem analysis
    "preferred_states": ["FOCUSED", "ANALYTICAL"]
}

support_vortex = VortexCore(
    max_context_tokens=support_config["max_context_tokens"],
    working_memory_size=support_config["working_memory_size"]
)
```

### Casual Chat Configuration

```python
# Optimized for casual conversation
casual_config = {
    "max_context_tokens": 3000,       # Smaller context
    "working_memory_size": 6,         # Standard memory
    "rolling_window_size": 8,         # Normal flow
    "importance_threshold": 0.8,      # Standard archiving
    "consciousness_enabled": True,    # For natural flow
    "preferred_states": ["CONVERSATIONAL"]
}

casual_vortex = VortexCore(
    max_context_tokens=casual_config["max_context_tokens"],
    working_memory_size=casual_config["working_memory_size"]
)
```

## 🔄 Runtime Configuration

### Dynamic Configuration Changes

```python
class ConfigurableVortex:
    def __init__(self, initial_config=None):
        self.config = initial_config or {}
        self.vortex = self._create_vortex()
    
    def _create_vortex(self):
        return VortexCore(
            max_context_tokens=self.config.get('max_context_tokens', 8000),
            working_memory_size=self.config.get('working_memory_size', 7),
            lmstudio_url=self.config.get('lmstudio_url', 'http://localhost:1234'),
            embedding_model=self.config.get('embedding_model'),
            db_path=self.config.get('db_path', 'vortex_consciousness.db')
        )
    
    def update_config(self, new_config):
        """Update configuration and recreate VORTEX if needed"""
        old_config = self.config.copy()
        self.config.update(new_config)
        
        # Check if core parameters changed
        core_params = ['max_context_tokens', 'working_memory_size', 'lmstudio_url', 'embedding_model', 'db_path']
        if any(old_config.get(param) != self.config.get(param) for param in core_params):
            # Shutdown old instance
            self.vortex.shutdown()
            # Create new instance
            self.vortex = self._create_vortex()
            print("VORTEX recreated with new configuration")
    
    def get_config(self):
        return self.config.copy()

# Usage
configurable_vortex = ConfigurableVortex({
    'max_context_tokens': 4000,
    'working_memory_size': 5
})

# Later, update configuration
configurable_vortex.update_config({
    'max_context_tokens': 8000,
    'working_memory_size': 10
})
```

### Configuration Profiles

```python
class VortexProfileManager:
    def __init__(self):
        self.profiles = {
            'research': {
                'max_context_tokens': 12000,
                'working_memory_size': 12,
                'consciousness_enabled': True
            },
            'creative': {
                'max_context_tokens': 8000,
                'working_memory_size': 10,
                'consciousness_enabled': True
            },
            'support': {
                'max_context_tokens': 4000,
                'working_memory_size': 5,
                'consciousness_enabled': True
            },
            'casual': {
                'max_context_tokens': 3000,
                'working_memory_size': 6,
                'consciousness_enabled': True
            }
        }
        self.current_profile = None
        self.vortex = None
    
    def load_profile(self, profile_name):
        """Load a configuration profile"""
        if profile_name not in self.profiles:
            raise ValueError(f"Profile '{profile_name}' not found")
        
        config = self.profiles[profile_name]
        
        # Shutdown existing instance
        if self.vortex:
            self.vortex.shutdown()
        
        # Create new instance with profile
        self.vortex = VortexCore(**config)
        self.current_profile = profile_name
        
        print(f"Loaded profile: {profile_name}")
        return self.vortex
    
    def add_profile(self, name, config):
        """Add a new configuration profile"""
        self.profiles[name] = config
    
    def list_profiles(self):
        """List available profiles"""
        return list(self.profiles.keys())

# Usage
profile_manager = VortexProfileManager()

# Load research profile
research_vortex = profile_manager.load_profile('research')

# Switch to creative profile
creative_vortex = profile_manager.load_profile('creative')

# Add custom profile
profile_manager.add_profile('custom', {
    'max_context_tokens': 6000,
    'working_memory_size': 8,
    'consciousness_enabled': True
})
```

## 🔍 Configuration Validation

```python
def validate_config(config):
    """Validate VORTEX configuration"""
    errors = []
    
    # Validate max_context_tokens
    max_tokens = config.get('max_context_tokens', 8000)
    if not isinstance(max_tokens, int) or max_tokens < 1000:
        errors.append("max_context_tokens must be an integer >= 1000")
    
    # Validate working_memory_size
    memory_size = config.get('working_memory_size', 7)
    if not isinstance(memory_size, int) or memory_size < 3 or memory_size > 15:
        errors.append("working_memory_size must be an integer between 3 and 15")
    
    # Validate lmstudio_url
    url = config.get('lmstudio_url', 'http://localhost:1234')
    if not isinstance(url, str) or not url.startswith('http'):
        errors.append("lmstudio_url must be a valid HTTP URL")
    
    # Validate importance_threshold (if present)
    threshold = config.get('importance_threshold')
    if threshold is not None:
        if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 1:
            errors.append("importance_threshold must be a number between 0 and 1")
    
    return errors

# Usage
config = {
    'max_context_tokens': 8000,
    'working_memory_size': 7,
    'lmstudio_url': 'http://localhost:1234'
}

errors = validate_config(config)
if errors:
    print("Configuration errors:")
    for error in errors:
        print(f"  - {error}")
else:
    print("Configuration is valid")
    vortex = VortexCore(**config)
```

## 📊 Configuration Monitoring

```python
def monitor_configuration_performance(vortex, duration=60):
    """Monitor VORTEX performance with current configuration"""
    import time
    import psutil
    import os
    
    start_time = time.time()
    process = psutil.Process(os.getpid())
    
    initial_memory = process.memory_info().rss / 1024 / 1024
    
    print(f"Monitoring VORTEX performance for {duration} seconds...")
    print(f"Initial memory usage: {initial_memory:.1f} MB")
    
    # Add some test data
    for i in range(10):
        vortex.add_context(f"Test message {i}", "user")
        time.sleep(1)
    
    # Check final stats
    final_memory = process.memory_info().rss / 1024 / 1024
    memory_increase = final_memory - initial_memory
    
    report = vortex.get_consciousness_report()
    
    print(f"Final memory usage: {final_memory:.1f} MB")
    print(f"Memory increase: {memory_increase:.1f} MB")
    print(f"Total chunks processed: {report['statistics']['total_chunks_processed']}")
    print(f"Consciousness active: {report['consciousness_active']}")
    
    return {
        'memory_increase': memory_increase,
        'chunks_processed': report['statistics']['total_chunks_processed'],
        'consciousness_active': report['consciousness_active']
    }

# Usage
performance = monitor_configuration_performance(vortex)
```

---

**Next**: Learn about [Advanced Features](./tutorials/advanced-features.md) to make the most of your configuration.
