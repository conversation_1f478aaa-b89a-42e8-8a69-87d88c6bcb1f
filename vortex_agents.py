"""
VORTEX AGENTS: Multi-Agent Consciousness Orchestration
Specialized AI agents that work together with the Vortex consciousness system
"""

import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import threading
from queue import Queue, PriorityQueue
import uuid

from vortex_core import VortexCore, ContextChunk


class AgentType(Enum):
    """Types of specialized agents"""
    RESEARCHER = "researcher"
    ANALYST = "analyst"
    CREATIVE = "creative"
    SYNTHESIZER = "synthesizer"


class AgentState(Enum):
    """Agent operational states"""
    IDLE = "idle"
    THINKING = "thinking"
    PROCESSING = "processing"


@dataclass
class AgentTask:
    """Task for an agent to execute"""
    id: str
    agent_type: AgentType
    description: str
    priority: int
    context: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)


class BaseAgent(ABC):
    """Base class for all Vortex agents"""
    
    def __init__(self, agent_id: str, agent_type: AgentType, vortex_core: VortexCore):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.vortex_core = vortex_core
        self.state = AgentState.IDLE
        
        # Performance metrics
        self.stats = {
            'tasks_completed': 0,
            'average_response_time': 0.0
        }
        
        # Agent specialization
        self.system_prompt = self._get_system_prompt()
        self.capabilities = self._get_capabilities()
    
    @abstractmethod
    def _get_system_prompt(self) -> str:
        """Get the system prompt for this agent type"""
        pass
    
    @abstractmethod
    def _get_capabilities(self) -> List[str]:
        """Get list of capabilities this agent provides"""
        pass
    
    @abstractmethod
    def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """Process a specific task"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type.value,
            'state': self.state.value,
            'stats': self.stats.copy(),
            'capabilities': self.capabilities.copy()
        }


class ResearchAgent(BaseAgent):
    """Agent specialized in research and information gathering"""
    
    def __init__(self, agent_id: str, vortex_core: VortexCore):
        super().__init__(agent_id, AgentType.RESEARCHER, vortex_core)
    
    def _get_system_prompt(self) -> str:
        return """You are a Research Agent specialized in gathering and analyzing information.
        You excel at finding relevant data, fact-checking, and synthesizing knowledge."""
    
    def _get_capabilities(self) -> List[str]:
        return ["information_gathering", "fact_verification", "knowledge_synthesis"]
    
    def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """Process research task"""
        self.state = AgentState.THINKING
        start_time = time.time()
        
        try:
            query = task.context.get("query", task.description)
            domain = task.context.get("domain", "general")
            
            # Simulate research process
            research_result = self._conduct_research(query, domain)
            
            # Add findings to context
            findings_content = f"Research findings for '{query}': {research_result['summary']}"
            self.vortex_core.add_context(findings_content, "research")
            
            processing_time = time.time() - start_time
            self.stats['tasks_completed'] += 1
            
            return {
                'success': True,
                'result': research_result,
                'processing_time': processing_time,
                'agent_id': self.agent_id
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e), 'agent_id': self.agent_id}
        finally:
            self.state = AgentState.IDLE
    
    def _conduct_research(self, query: str, domain: str) -> Dict[str, Any]:
        """Simulate research process"""
        research_points = [
            f"Key concept analysis for: {query}",
            f"Historical context and background",
            f"Current developments and trends",
            f"Related fields and connections"
        ]
        
        return {
            'query': query,
            'domain': domain,
            'research_points': research_points,
            'summary': f"Comprehensive research on {query} in {domain} domain",
            'confidence': 0.85,
            'key_insights': [f"Primary insight about {query}", f"Pattern in {domain}"]
        }


class AnalysisAgent(BaseAgent):
    """Agent specialized in data analysis and logical reasoning"""
    
    def __init__(self, agent_id: str, vortex_core: VortexCore):
        super().__init__(agent_id, AgentType.ANALYST, vortex_core)
    
    def _get_system_prompt(self) -> str:
        return """You are an Analysis Agent specialized in logical reasoning and data analysis.
        You excel at pattern recognition, critical thinking, and structured analysis."""
    
    def _get_capabilities(self) -> List[str]:
        return ["logical_reasoning", "pattern_recognition", "critical_thinking"]
    
    def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """Process analysis task"""
        self.state = AgentState.THINKING
        start_time = time.time()
        
        try:
            data = task.context.get("data", task.description)
            analysis_type = task.context.get("type", "general")
            
            analysis_result = self._perform_analysis(data, analysis_type)
            
            # Add analysis to context
            analysis_content = f"Analysis of {analysis_type}: {analysis_result['conclusion']}"
            self.vortex_core.add_context(analysis_content, "analysis")
            
            processing_time = time.time() - start_time
            self.stats['tasks_completed'] += 1
            
            return {
                'success': True,
                'result': analysis_result,
                'processing_time': processing_time,
                'agent_id': self.agent_id
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e), 'agent_id': self.agent_id}
        finally:
            self.state = AgentState.IDLE
    
    def _perform_analysis(self, data: str, analysis_type: str) -> Dict[str, Any]:
        """Perform analysis"""
        return {
            'data_analyzed': data,
            'type': analysis_type,
            'key_findings': [f"Pattern in {analysis_type}", "Statistical significance"],
            'conclusion': f"Analysis reveals patterns in {analysis_type}",
            'confidence': 0.88
        }


class CreativeAgent(BaseAgent):
    """Agent specialized in creative thinking and ideation"""
    
    def __init__(self, agent_id: str, vortex_core: VortexCore):
        super().__init__(agent_id, AgentType.CREATIVE, vortex_core)
    
    def _get_system_prompt(self) -> str:
        return """You are a Creative Agent specialized in innovative thinking.
        You excel at generating novel ideas and creative solutions."""
    
    def _get_capabilities(self) -> List[str]:
        return ["creative_thinking", "ideation", "innovation"]
    
    def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """Process creative task"""
        self.state = AgentState.THINKING
        start_time = time.time()
        
        try:
            challenge = task.context.get("challenge", task.description)
            
            creative_result = self._generate_creative_solutions(challenge)
            
            # Add creativity to context
            creative_content = f"Creative solutions for '{challenge}': {creative_result['best_idea']}"
            self.vortex_core.add_context(creative_content, "creative")
            
            processing_time = time.time() - start_time
            self.stats['tasks_completed'] += 1
            
            return {
                'success': True,
                'result': creative_result,
                'processing_time': processing_time,
                'agent_id': self.agent_id
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e), 'agent_id': self.agent_id}
        finally:
            self.state = AgentState.IDLE
    
    def _generate_creative_solutions(self, challenge: str) -> Dict[str, Any]:
        """Generate creative solutions"""
        ideas = [
            f"Revolutionary approach to {challenge}",
            f"Biomimetic solution inspired by nature",
            f"Collaborative community-driven solution"
        ]
        
        return {
            'challenge': challenge,
            'generated_ideas': ideas,
            'best_idea': ideas[0],
            'creativity_score': 0.92
        }


class AgentOrchestrator:
    """Orchestrates multiple agents and manages their collaboration"""
    
    def __init__(self, vortex_core: VortexCore):
        self.vortex_core = vortex_core
        self.agents: Dict[str, BaseAgent] = {}
        self.task_queue = PriorityQueue()
        
        # Performance tracking
        self.orchestration_stats = {
            'tasks_orchestrated': 0,
            'agent_utilization': {}
        }
    
    def add_agent(self, agent: BaseAgent):
        """Add an agent to the orchestration"""
        self.agents[agent.agent_id] = agent
        self.orchestration_stats['agent_utilization'][agent.agent_id] = 0
    
    def submit_task(self, description: str, agent_type: AgentType, 
                   priority: int = 1, context: Dict[str, Any] = None):
        """Submit a task to the orchestration system"""
        task = AgentTask(
            id=str(uuid.uuid4()),
            agent_type=agent_type,
            description=description,
            priority=priority,
            context=context or {}
        )
        
        self.task_queue.put((priority, task))
        self.orchestration_stats['tasks_orchestrated'] += 1
        
        # Process task immediately for demo
        return self._process_task_now(task)
    
    def _process_task_now(self, task: AgentTask):
        """Process task immediately"""
        suitable_agents = [
            agent for agent in self.agents.values() 
            if agent.agent_type == task.agent_type
        ]
        
        if suitable_agents:
            agent = suitable_agents[0]
            self.orchestration_stats['agent_utilization'][agent.agent_id] += 1
            return agent.process_task(task)
        
        return {'success': False, 'error': 'No suitable agent found'}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        return {
            'active_agents': len(self.agents),
            'pending_tasks': self.task_queue.qsize(),
            'orchestration_stats': self.orchestration_stats.copy(),
            'agent_statuses': {aid: agent.get_status() for aid, agent in self.agents.items()},
            'consciousness_report': self.vortex_core.get_consciousness_report()
        } 