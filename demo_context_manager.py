"""
Interactive Demo for Advanced Context Window Manager
Demonstrates real-time context optimization with LMStudio
"""

import time
import json
from context_manager import <PERSON>text<PERSON><PERSON>owManager, ConversationSimulator
from typing import List, Dict


class InteractiveDemo:
    """Interactive demonstration of the context window manager"""
    
    def __init__(self):
        self.manager = ContextWindowManager(
            max_window_size=6000,  # Adjust based on your model's context limit
            rolling_window_size=8,
            lmstudio_url="http://localhost:1234",  # Adjust to your LMStudio URL
            embedding_model="text-embedding-ada-002"  # Or your embedding model
        )
        self.conversation_active = True
    
    def print_banner(self):
        """Print demo banner"""
        print("🧠 " + "="*60)
        print("   ADVANCED CONTEXT WINDOW MANAGER DEMO")
        print("   Intelligent Memory for Large Language Models")
        print("="*62)
        print()
        print("Features demonstrated:")
        print("  🔄 Rolling Window - Recent conversation context")
        print("  🔍 Semantic Search - Retrieve relevant past context")
        print("  📊 Importance Weighting - Prioritize valuable content")
        print("  💾 Persistent Memory - Archive and recall conversations")
        print("  🎯 Context Optimization - Smart token management")
        print()
    
    def demo_basic_functionality(self):
        """Demonstrate basic context management functionality"""
        print("🎯 DEMO 1: Basic Context Management")
        print("-" * 40)
        
        # Add some messages to build context
        topics = [
            ("Tell me about quantum computing and its applications", "user"),
            ("Quantum computing uses quantum mechanics principles like superposition and entanglement to process information. Key applications include cryptography, optimization, and drug discovery.", "assistant"),
            ("What about machine learning integration with quantum systems?", "user"),
            ("Quantum machine learning combines quantum computing with ML algorithms, potentially offering exponential speedups for certain problems like pattern recognition and optimization.", "assistant"),
            ("Can you explain the current limitations?", "user"),
            ("Current limitations include quantum decoherence, limited qubit counts, high error rates, and the need for extremely low temperatures. Most quantum computers today are still experimental.", "assistant")
        ]
        
        print("Adding conversation messages...")
        for content, role in topics:
            chunk = self.manager.add_message(content, role)
            print(f"  {role.upper()}: {content[:60]}{'...' if len(content) > 60 else ''}")
            time.sleep(0.5)  # Simulate real conversation timing
        
        print(f"\n📊 Current Status:")
        status = self.manager.get_status()
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print("\n" + "="*60 + "\n")
    
    def demo_semantic_retrieval(self):
        """Demonstrate semantic similarity retrieval"""
        print("🔍 DEMO 2: Semantic Context Retrieval")
        print("-" * 40)
        
        # Add more diverse content to test semantic search
        additional_topics = [
            ("Let's talk about space exploration and Mars missions", "user"),
            ("Space exploration has advanced significantly with private companies like SpaceX developing reusable rockets. Mars missions are planned for the 2030s.", "assistant"),
            ("What about cooking Italian pasta dishes?", "user"),
            ("Italian pasta cooking requires al dente texture, quality ingredients, and proper sauce pairing. Key techniques include salting water and not overcooking.", "assistant"),
            ("How do neural networks work in deep learning?", "user"),
            ("Neural networks use layers of interconnected nodes to process data, learning patterns through backpropagation and gradient descent optimization.", "assistant")
        ]
        
        print("Adding diverse conversation topics...")
        for content, role in additional_topics:
            self.manager.add_message(content, role)
            print(f"  Added: {content[:50]}...")
        
        # Test semantic retrieval
        test_queries = [
            "Tell me more about quantum algorithms",
            "What are the challenges in space travel?", 
            "How do I make perfect pasta?",
            "Explain artificial intelligence concepts"
        ]
        
        print("\n🔍 Testing Semantic Retrieval:")
        for query in test_queries:
            print(f"\nQuery: '{query}'")
            context = self.manager.get_optimized_context(query)
            print(f"Retrieved {len(context)} relevant context messages:")
            
            for i, msg in enumerate(context[-3:]):  # Show last 3 for brevity
                content_preview = msg['content'][:80] + "..." if len(msg['content']) > 80 else msg['content']
                print(f"  {i+1}. [{msg['role'].upper()}] {content_preview}")
        
        print("\n" + "="*60 + "\n")
    
    def demo_conversation_analysis(self):
        """Demonstrate conversation pattern analysis"""
        print("📊 DEMO 3: Conversation Pattern Analysis")
        print("-" * 40)
        
        # Simulate a longer conversation
        simulator = ConversationSimulator(self.manager)
        print("Simulating extended conversation...")
        analysis = simulator.simulate_conversation(12)
        
        print("\n📈 Conversation Analysis Results:")
        for key, value in analysis.items():
            if key == "most_accessed_content":
                print(f"  {key}:")
                for content, count in value:
                    print(f"    - '{content}' (accessed {count} times)")
            else:
                print(f"  {key}: {value}")
        
        print("\n" + "="*60 + "\n")
    
    def demo_context_optimization(self):
        """Demonstrate context window optimization"""
        print("🎯 DEMO 4: Context Window Optimization")
        print("-" * 40)
        
        # Fill up context to test optimization
        print("Adding many messages to test context optimization...")
        for i in range(15):
            long_message = f"This is message {i+1} with substantial content to test context window management. " * 10
            self.manager.add_message(long_message, "user" if i % 2 == 0 else "assistant")
        
        # Test optimization with different queries
        query = "Summarize our entire conversation about technology and science"
        optimized_context = self.manager.get_optimized_context(query)
        
        print(f"\n🔧 Context Optimization Results:")
        print(f"  Total conversation messages: {len(self.manager.conversation_history)}")
        print(f"  Rolling window size: {len(self.manager.rolling_window)}")
        print(f"  Archived chunks: {len(self.manager.archived_chunks)}")
        print(f"  Optimized context messages: {len(optimized_context)}")
        
        # Estimate token usage
        total_chars = sum(len(msg['content']) for msg in optimized_context)
        estimated_tokens = total_chars // 4  # Rough estimation
        print(f"  Estimated tokens in optimized context: {estimated_tokens}")
        print(f"  Max allowed tokens: {self.manager.max_window_size}")
        print(f"  Token efficiency: {(estimated_tokens/self.manager.max_window_size)*100:.1f}%")
        
        print("\n" + "="*60 + "\n")
    
    def interactive_chat_demo(self):
        """Interactive chat demonstration"""
        print("💬 DEMO 5: Interactive Chat with Context Management")
        print("-" * 40)
        print("Type your messages (or 'quit' to exit, 'status' for stats, 'export' to save)")
        print()
        
        while self.conversation_active:
            try:
                user_input = input("You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                elif user_input.lower() == 'status':
                    status = self.manager.get_status()
                    analysis = self.manager.analyze_conversation_patterns()
                    print("\n📊 Current Status:")
                    for key, value in status.items():
                        print(f"  {key}: {value}")
                    print("\n📈 Analysis:")
                    for key, value in analysis.items():
                        if key != "most_accessed_content":
                            print(f"  {key}: {value}")
                    print()
                    continue
                elif user_input.lower() == 'export':
                    filename = self.manager.export_conversation()
                    print(f"💾 Conversation exported to: {filename}\n")
                    continue
                elif not user_input:
                    continue
                
                # Add user message
                self.manager.add_message(user_input, "user")
                
                # Get optimized context for response
                context = self.manager.get_optimized_context(user_input)
                
                # Simulate LLM response (in real usage, this would call LMStudio)
                response = self.simulate_llm_response(user_input, context)
                
                # Add assistant response
                self.manager.add_message(response, "assistant")
                
                print(f"Assistant: {response}")
                print(f"[Context used: {len(context)} messages]\n")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}\n")
        
        print("\n👋 Chat demo ended!")
        print("=" * 60 + "\n")
    
    def simulate_llm_response(self, user_input: str, context: List[Dict]) -> str:
        """Simulate LLM response (replace with actual LMStudio call)"""
        # This is a placeholder - in real usage, you'd send context to LMStudio
        responses = [
            f"Based on our conversation context, I understand you're asking about '{user_input[:30]}...'. Here's my response considering our previous discussion.",
            f"That's an interesting question about '{user_input[:30]}...'. Let me provide a comprehensive answer.",
            f"Building on our earlier conversation, regarding '{user_input[:30]}...', I can share the following insights.",
            f"Your question about '{user_input[:30]}...' connects well with what we discussed earlier. Here's my detailed response."
        ]
        
        import random
        return random.choice(responses)
    
    def run_full_demo(self):
        """Run the complete demonstration"""
        self.print_banner()
        
        try:
            self.demo_basic_functionality()
            time.sleep(2)
            
            self.demo_semantic_retrieval()
            time.sleep(2)
            
            self.demo_conversation_analysis()
            time.sleep(2)
            
            self.demo_context_optimization()
            time.sleep(2)
            
            self.interactive_chat_demo()
            
        except KeyboardInterrupt:
            print("\n\n⏹️  Demo interrupted by user")
        
        # Final statistics
        print("🏁 FINAL DEMO STATISTICS")
        print("-" * 40)
        final_analysis = self.manager.analyze_conversation_patterns()
        for key, value in final_analysis.items():
            if key == "most_accessed_content":
                print(f"  {key}:")
                for content, count in value[:3]:  # Top 3
                    print(f"    - '{content}' (accessed {count} times)")
            else:
                print(f"  {key}: {value}")
        
        # Export final conversation
        export_file = self.manager.export_conversation("demo_conversation_export.json")
        print(f"\n💾 Full demo conversation exported to: {export_file}")
        
        print("\n🎉 Demo completed successfully!")
        print("You now have a working context window manager for LMStudio!")


if __name__ == "__main__":
    demo = InteractiveDemo()
    demo.run_full_demo()