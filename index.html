<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space Physics Simulation</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="container">
        <div id="canvas-container">
            <canvas id="simulation-canvas"></canvas>
        </div>
        
        <div id="controls-panel">
            <h2>Space Physics Simulation</h2>
            
            <div class="control-section">
                <h3>Simulation Controls</h3>
                <div class="control-group">
                    <label for="timeScale">Time Scale:</label>
                    <input type="range" id="timeScale" min="0.1" max="5" step="0.1" value="1">
                    <span id="timeScaleValue">1.0x</span>
                </div>
                
                <div class="control-group">
                    <label for="gravitationalConstant">Gravitational Constant:</label>
                    <input type="range" id="gravitationalConstant" min="0.1" max="2" step="0.1" value="1">
                    <span id="gravitationalConstantValue">1.0</span>
                </div>
                
                <div class="control-group">
                    <button id="pausePlay">Pause</button>
                    <button id="reset">Reset</button>
                </div>
            </div>

            <div class="control-section">
                <h3>Visualization</h3>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showOrbits" checked>
                        Show Orbital Paths
                    </label>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showVelocityVectors">
                        Show Velocity Vectors
                    </label>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showGrid">
                        Show Grid
                    </label>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showGravityField">
                        Show Gravity Field
                    </label>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="showLagrangePoints">
                        Show Lagrange Points
                    </label>
                </div>
            </div>

            <div class="control-section">
                <h3>Advanced Physics</h3>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="enableTidalForces" checked>
                        Enable Tidal Forces
                    </label>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="enableRelativisticEffects">
                        Enable Relativistic Effects
                    </label>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="enableGravitationalWaves">
                        Enable Gravitational Waves
                    </label>
                </div>
                
                <div class="control-group">
                    <label for="tidalForceStrength">Tidal Force Strength:</label>
                    <input type="range" id="tidalForceStrength" min="0" max="2" step="0.1" value="1">
                    <span id="tidalForceStrengthValue">1.0</span>
                </div>
            </div>

            <div class="control-section">
                <h3>Add Celestial Bodies</h3>
                <div class="control-group">
                    <button id="addSun">Add Sun</button>
                    <button id="addPlanet">Add Planet</button>
                </div>
                
                <div class="control-group">
                    <label for="bodyMass">Mass:</label>
                    <input type="range" id="bodyMass" min="0.1" max="10" step="0.1" value="1">
                    <span id="bodyMassValue">1.0</span>
                </div>
                
                <div class="control-group">
                    <label for="bodyRadius">Radius:</label>
                    <input type="range" id="bodyRadius" min="0.5" max="5" step="0.1" value="1">
                    <span id="bodyRadiusValue">1.0</span>
                </div>
                
                <div class="control-group">
                    <label for="initialVelocity">Initial Velocity:</label>
                    <input type="range" id="initialVelocity" min="0" max="5" step="0.1" value="2">
                    <span id="initialVelocityValue">2.0</span>
                </div>
            </div>

            <div class="control-section">
                <h3>Camera Controls</h3>
                <div class="control-group">
                    <small>Mouse: Orbit camera<br>
                    Wheel: Zoom<br>
                    Right-click + drag: Pan</small>
                </div>
            </div>

            <div class="control-section">
                <h3>Information</h3>
                <div id="info">
                    <div>Bodies: <span id="bodyCount">0</span></div>
                    <div>Total Energy: <span id="totalEnergy">0</span></div>
                    <div>FPS: <span id="fps">0</span></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="physics.js"></script>
    <script src="simulation.js"></script>
</body>
</html>