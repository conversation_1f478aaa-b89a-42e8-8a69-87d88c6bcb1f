"""
Simple Context Manager Demo
Direct demonstration without complex dependency management
"""

import sys
import os

def main():
    print("🧠 Context Window Manager - Simple Demo")
    print("=" * 50)
    
    try:
        # Import the context manager
        from context_manager import ContextWindowManager, ConversationSimulator
        print("✅ Context manager imported successfully!")
        
        # Initialize with basic settings
        print("\n🔧 Initializing context manager...")
        manager = ContextWindowManager(
            max_window_size=4000,
            rolling_window_size=8,
            lmstudio_url="http://localhost:1234"
        )
        print("✅ Context manager initialized!")
        
        # Add some sample conversation
        print("\n💬 Adding sample conversation...")
        conversation = [
            ("Hello! Can you help me understand quantum computing?", "user"),
            ("I'd be happy to help! Quantum computing uses quantum mechanics principles like superposition and entanglement to process information in ways classical computers cannot.", "assistant"),
            ("What are the main applications of quantum computing?", "user"),
            ("Key applications include cryptography, drug discovery, financial modeling, optimization problems, and machine learning acceleration.", "assistant"),
            ("How does it compare to classical computing?", "user"),
            ("Quantum computers can potentially solve certain problems exponentially faster than classical computers, but they're still experimental and face challenges like decoherence.", "assistant"),
            ("Can you tell me about machine learning?", "user"),
            ("Machine learning is a subset of AI that enables computers to learn and improve from experience without being explicitly programmed.", "assistant")
        ]
        
        for content, role in conversation:
            chunk = manager.add_message(content, role)
            print(f"  Added {role}: {content[:50]}...")
        
        # Test semantic retrieval
        print("\n🔍 Testing semantic context retrieval...")
        test_queries = [
            "What are quantum computing applications?",
            "Tell me about AI and learning algorithms",
            "How do quantum and classical computers differ?"
        ]
        
        for query in test_queries:
            print(f"\n📝 Query: '{query}'")
            context = manager.get_optimized_context(query)
            print(f"   Retrieved {len(context)} context messages")
            
            # Show the most relevant context
            if context:
                print("   Most relevant context:")
                for i, msg in enumerate(context[-2:]):  # Show last 2 messages
                    preview = msg['content'][:80] + "..." if len(msg['content']) > 80 else msg['content']
                    print(f"     {i+1}. [{msg['role'].upper()}] {preview}")
        
        # Show conversation analysis
        print("\n📊 Conversation Analysis:")
        analysis = manager.analyze_conversation_patterns()
        print(f"   Total messages: {analysis['total_messages']}")
        print(f"   User messages: {analysis['user_messages']}")
        print(f"   Assistant messages: {analysis['assistant_messages']}")
        print(f"   Average importance: {analysis['average_importance']:.2f}")
        print(f"   Archived chunks: {analysis['archived_chunks']}")
        
        # Show current status
        print("\n📈 Current Status:")
        status = manager.get_status()
        print(f"   Rolling window: {status['rolling_window_size']}/{status['max_rolling_size']}")
        print(f"   Total conversation length: {status['total_conversation_length']}")
        print(f"   Semantic retrievals: {status['statistics']['semantic_retrievals']}")
        print(f"   Context optimizations: {status['statistics']['context_optimizations']}")
        
        # Test export functionality
        print("\n💾 Testing export...")
        filename = manager.export_conversation("demo_conversation.json")
        print(f"   Conversation exported to: {filename}")
        
        # Simulate a more complex conversation
        print("\n🎭 Running conversation simulation...")
        simulator = ConversationSimulator(manager)
        sim_results = simulator.simulate_conversation(5)
        
        print(f"   Simulation completed with {len(sim_results)} exchanges")
        
        # Final analysis
        print("\n🏁 Final Analysis:")
        final_analysis = manager.analyze_conversation_patterns()
        print(f"   Total messages: {final_analysis['total_messages']}")
        print(f"   Memory efficiency: {len(manager.archived_chunks)} archived chunks")
        print(f"   Semantic retrievals: {final_analysis['statistics']['semantic_retrievals']}")
        
        print("\n🎉 Demo completed successfully!")
        print("\n" + "=" * 50)
        print("🚀 Your Context Window Manager is working perfectly!")
        print("💡 Key Features Demonstrated:")
        print("   ✅ Rolling window management")
        print("   ✅ Semantic similarity search")
        print("   ✅ Context optimization")
        print("   ✅ Conversation analysis")
        print("   ✅ Export/import functionality")
        print("   ✅ Memory efficiency")
        
        print("\n🔧 Next Steps:")
        print("   1. Start LMStudio on http://localhost:1234")
        print("   2. Run: python web_interface.py")
        print("   3. Open http://localhost:5000 in your browser")
        print("   4. Experience the full visual interface!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed:")
        print("pip install numpy requests flask")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("Check the error details above for troubleshooting.")

if __name__ == "__main__":
    main()