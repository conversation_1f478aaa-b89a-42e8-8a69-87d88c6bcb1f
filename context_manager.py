"""
Advanced Context Window Manager for LMStudio
Combines rolling windows with semantic similarity for intelligent context management
"""

import json
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import deque
import numpy as np
import requests
import hashlib
import sqlite3
import os


@dataclass
class ContextChunk:
    """Represents a chunk of conversation context"""
    id: str
    content: str
    timestamp: float
    role: str  # 'user', 'assistant', 'system'
    embedding: Optional[List[float]] = None
    importance: float = 1.0
    access_count: int = 0
    last_accessed: float = 0.0
    semantic_tags: List[str] = None
    
    def __post_init__(self):
        if self.semantic_tags is None:
            self.semantic_tags = []
        if self.last_accessed == 0.0:
            self.last_accessed = self.timestamp


class LMStudioClient:
    """Client for LMStudio API interactions"""
    
    def __init__(self, base_url: str = "http://localhost:1234", embedding_model: str = None):
        self.base_url = base_url
        self.embedding_model = embedding_model
        self.session = requests.Session()
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using LMStudio embedding model"""
        try:
            response = self.session.post(
                f"{self.base_url}/v1/embeddings",
                json={
                    "input": text,
                    "model": self.embedding_model or "text-embedding-ada-002"
                },
                timeout=30
            )
            response.raise_for_status()
            return response.json()["data"][0]["embedding"]
        except Exception as e:
            print(f"Warning: Could not get embedding: {e}")
            # Fallback to simple hash-based pseudo-embedding
            return self._pseudo_embedding(text)
    
    def _pseudo_embedding(self, text: str, dim: int = 768) -> List[float]:
        """Fallback pseudo-embedding using text hash"""
        hash_obj = hashlib.md5(text.encode())
        np.random.seed(int(hash_obj.hexdigest(), 16) % (2**32))
        return np.random.randn(dim).tolist()
    
    def chat_completion(self, messages: List[Dict], **kwargs) -> str:
        """Send chat completion request to LMStudio"""
        try:
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json={
                    "messages": messages,
                    "stream": False,
                    **kwargs
                },
                timeout=60
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            return f"Error: {e}"


class SemanticIndex:
    """Manages semantic similarity search using embeddings"""
    
    def __init__(self, db_path: str = "context_index.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database for storing embeddings"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS embeddings (
                chunk_id TEXT PRIMARY KEY,
                embedding BLOB,
                content TEXT,
                timestamp REAL,
                importance REAL,
                access_count INTEGER,
                last_accessed REAL
            )
        ''')
        conn.commit()
        conn.close()
    
    def store_embedding(self, chunk: ContextChunk):
        """Store chunk embedding in database"""
        if chunk.embedding is None:
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        embedding_blob = np.array(chunk.embedding).tobytes()
        cursor.execute('''
            INSERT OR REPLACE INTO embeddings 
            (chunk_id, embedding, content, timestamp, importance, access_count, last_accessed)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (chunk.id, embedding_blob, chunk.content, chunk.timestamp, 
              chunk.importance, chunk.access_count, chunk.last_accessed))
        
        conn.commit()
        conn.close()
    
    def find_similar(self, query_embedding: List[float], top_k: int = 5, 
                    min_similarity: float = 0.7) -> List[Tuple[str, float]]:
        """Find similar chunks using cosine similarity"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT chunk_id, embedding FROM embeddings')
        results = []
        query_vec = np.array(query_embedding)
        
        for chunk_id, embedding_blob in cursor.fetchall():
            stored_vec = np.frombuffer(embedding_blob, dtype=np.float64)
            similarity = self._cosine_similarity(query_vec, stored_vec)
            
            if similarity >= min_similarity:
                results.append((chunk_id, similarity))
        
        conn.close()
        
        # Sort by similarity and return top_k
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:top_k]
    
    def _cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    
    def update_access(self, chunk_id: str):
        """Update access statistics for a chunk"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE embeddings 
            SET access_count = access_count + 1, last_accessed = ?
            WHERE chunk_id = ?
        ''', (time.time(), chunk_id))
        
        conn.commit()
        conn.close()


class ContextWindowManager:
    """Advanced context window manager with rolling window and semantic indexing"""
    
    def __init__(self, 
                 max_window_size: int = 8000,  # tokens
                 rolling_window_size: int = 10,  # number of recent messages
                 lmstudio_url: str = "http://localhost:1234",
                 embedding_model: str = None,
                 importance_threshold: float = 0.8):
        
        self.max_window_size = max_window_size
        self.rolling_window_size = rolling_window_size
        self.importance_threshold = importance_threshold
        
        # Core components
        self.lm_client = LMStudioClient(lmstudio_url, embedding_model)
        self.semantic_index = SemanticIndex()
        
        # Storage
        self.rolling_window = deque(maxlen=rolling_window_size)
        self.archived_chunks: Dict[str, ContextChunk] = {}
        self.conversation_history: List[ContextChunk] = []
        
        # Statistics
        self.stats = {
            "total_chunks": 0,
            "archived_chunks": 0,
            "semantic_retrievals": 0,
            "context_optimizations": 0
        }
    
    def add_message(self, content: str, role: str = "user", 
                   importance: float = 1.0) -> ContextChunk:
        """Add a new message to the context manager"""
        
        # Create chunk
        chunk_id = hashlib.sha256(f"{content}{time.time()}".encode()).hexdigest()[:16]
        chunk = ContextChunk(
            id=chunk_id,
            content=content,
            timestamp=time.time(),
            role=role,
            importance=importance
        )
        
        # Get embedding
        chunk.embedding = self.lm_client.get_embedding(content)
        
        # Add to rolling window
        self.rolling_window.append(chunk)
        self.conversation_history.append(chunk)
        
        # Store in semantic index
        self.semantic_index.store_embedding(chunk)
        
        # Archive old chunks if rolling window is full
        if len(self.rolling_window) == self.rolling_window_size:
            self._archive_if_needed()
        
        self.stats["total_chunks"] += 1
        return chunk
    
    def get_optimized_context(self, query: str = None) -> List[Dict[str, str]]:
        """Get optimized context for LLM, combining rolling window + semantic retrieval"""
        
        context_messages = []
        
        # 1. Always include rolling window (recent context)
        rolling_context = []
        for chunk in self.rolling_window:
            rolling_context.append({
                "role": chunk.role,
                "content": chunk.content
            })
        
        # 2. If we have a query, add semantically relevant context
        semantic_context = []
        if query:
            query_embedding = self.lm_client.get_embedding(query)
            similar_chunks = self.semantic_index.find_similar(
                query_embedding, 
                top_k=3, 
                min_similarity=0.6
            )
            
            for chunk_id, similarity in similar_chunks:
                if chunk_id in self.archived_chunks:
                    chunk = self.archived_chunks[chunk_id]
                    semantic_context.append({
                        "role": "system",
                        "content": f"[Relevant context (similarity: {similarity:.2f})]: {chunk.content}"
                    })
                    
                    # Update access stats
                    chunk.access_count += 1
                    chunk.last_accessed = time.time()
                    self.semantic_index.update_access(chunk_id)
            
            self.stats["semantic_retrievals"] += len(similar_chunks)
        
        # 3. Combine contexts intelligently
        context_messages = semantic_context + rolling_context
        
        # 4. Ensure we don't exceed token limit
        context_messages = self._trim_to_token_limit(context_messages)
        
        return context_messages
    
    def _archive_if_needed(self):
        """Archive chunks that are being pushed out of rolling window"""
        if len(self.rolling_window) < self.rolling_window_size:
            return
        
        # The oldest chunk in rolling window will be replaced
        oldest_chunk = self.rolling_window[0]
        
        # Only archive if it meets importance threshold
        if oldest_chunk.importance >= self.importance_threshold:
            self.archived_chunks[oldest_chunk.id] = oldest_chunk
            self.stats["archived_chunks"] += 1
    
    def _trim_to_token_limit(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Trim messages to fit within token limit (rough estimation)"""
        total_tokens = 0
        trimmed_messages = []
        
        # Rough token estimation: ~4 characters per token
        for message in reversed(messages):
            message_tokens = len(message["content"]) // 4
            if total_tokens + message_tokens <= self.max_window_size:
                trimmed_messages.insert(0, message)
                total_tokens += message_tokens
            else:
                break
        
        if len(trimmed_messages) < len(messages):
            self.stats["context_optimizations"] += 1
        
        return trimmed_messages
    
    def analyze_conversation_patterns(self) -> Dict[str, Any]:
        """Analyze conversation patterns and provide insights"""
        if not self.conversation_history:
            return {"error": "No conversation history available"}
        
        # Basic statistics
        total_messages = len(self.conversation_history)
        user_messages = sum(1 for c in self.conversation_history if c.role == "user")
        assistant_messages = sum(1 for c in self.conversation_history if c.role == "assistant")
        
        # Calculate average importance
        avg_importance = sum(c.importance for c in self.conversation_history) / total_messages
        
        # Find most accessed archived content
        most_accessed = []
        for chunk in self.archived_chunks.values():
            if chunk.access_count > 0:
                most_accessed.append((chunk.content[:100], chunk.access_count))
        most_accessed.sort(key=lambda x: x[1], reverse=True)
        
        return {
            "total_messages": total_messages,
            "user_messages": user_messages,
            "assistant_messages": assistant_messages,
            "average_importance": avg_importance,
            "archived_chunks": len(self.archived_chunks),
            "most_accessed_content": most_accessed[:5],
            "statistics": self.stats
        }
    
    def export_conversation(self, filename: str = None) -> str:
        """Export full conversation history to JSON"""
        if filename is None:
            filename = f"conversation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "metadata": {
                "exported_at": datetime.now().isoformat(),
                "total_chunks": len(self.conversation_history),
                "archived_chunks": len(self.archived_chunks),
                "statistics": self.stats
            },
            "conversation": [asdict(chunk) for chunk in self.conversation_history],
            "archived": [asdict(chunk) for chunk in self.archived_chunks.values()]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return filename
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the context manager"""
        return {
            "rolling_window_size": len(self.rolling_window),
            "max_rolling_size": self.rolling_window_size,
            "archived_chunks": len(self.archived_chunks),
            "total_conversation_length": len(self.conversation_history),
            "statistics": self.stats,
            "current_time": datetime.now().isoformat()
        }


class ConversationSimulator:
    """Simulate conversations to test the context manager"""
    
    def __init__(self, context_manager: ContextWindowManager):
        self.context_manager = context_manager
        self.conversation_topics = [
            "machine learning algorithms",
            "space exploration",
            "cooking recipes",
            "programming languages",
            "climate change",
            "art and creativity",
            "technology trends",
            "philosophy of consciousness"
        ]
    
    def simulate_conversation(self, num_exchanges: int = 20):
        """Simulate a multi-topic conversation"""
        import random
        
        for i in range(num_exchanges):
            topic = random.choice(self.conversation_topics)
            
            # User message
            user_msg = f"Tell me about {topic} - I'm particularly interested in recent developments."
            self.context_manager.add_message(user_msg, "user", random.uniform(0.5, 1.0))
            
            # Assistant response
            assistant_msg = f"Here's what I know about {topic}: [This would be a detailed response about {topic} with current information and examples...]"
            self.context_manager.add_message(assistant_msg, "assistant", random.uniform(0.7, 1.0))
            
            print(f"Exchange {i+1}: {topic}")
        
        return self.context_manager.analyze_conversation_patterns()


if __name__ == "__main__":
    # Example usage and testing
    print("🧠 Advanced Context Window Manager for LMStudio")
    print("=" * 50)
    
    # Initialize context manager
    manager = ContextWindowManager(
        max_window_size=4000,
        rolling_window_size=6,
        lmstudio_url="http://localhost:1234"
    )
    
    # Simulate some conversation
    simulator = ConversationSimulator(manager)
    
    print("\n📝 Simulating conversation...")
    analysis = simulator.simulate_conversation(15)
    
    print("\n📊 Conversation Analysis:")
    for key, value in analysis.items():
        print(f"  {key}: {value}")
    
    print("\n🔍 Testing semantic retrieval...")
    query = "What are the latest developments in machine learning?"
    optimized_context = manager.get_optimized_context(query)
    
    print(f"\nOptimized context for query: '{query}'")
    print(f"Number of context messages: {len(optimized_context)}")
    
    print("\n📈 Context Manager Status:")
    status = manager.get_status()
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    # Export conversation
    export_file = manager.export_conversation()
    print(f"\n💾 Conversation exported to: {export_file}")