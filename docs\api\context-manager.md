# 🔄 Context Manager API Reference

The Context Manager provides traditional rolling window and semantic memory management for LLM conversations.

## 📋 Table of Contents

- [ContextWindowManager Class](#contextwindowmanager-class)
- [ContextChunk Class](#contextchunk-class)
- [SemanticIndex Class](#semanticindex-class)
- [LMStudioClient Class](#lmstudioclient-class)
- [ConversationSimulator Class](#conversationsimulator-class)
- [Usage Examples](#usage-examples)

## 🔄 ContextWindowManager Class

Main class for intelligent context window management with semantic memory.

### Constructor

```python
ContextWindowManager(
    max_window_size: int = 8000,
    rolling_window_size: int = 10,
    importance_threshold: float = 0.8,
    lmstudio_url: str = "http://localhost:1234",
    embedding_model: str = None
)
```

**Parameters:**
- `max_window_size`: Maximum tokens in context window
- `rolling_window_size`: Number of recent messages to keep in memory
- `importance_threshold`: Minimum importance score for archiving (0.0-1.0)
- `lmstudio_url`: LMStudio API endpoint URL
- `embedding_model`: Name of embedding model (auto-detected if None)

### Core Methods

#### `add_message(content: str, role: str = "user", importance: float = 1.0) -> ContextChunk`
Adds a new message to the context manager.

```python
manager = ContextWindowManager()

# Add user message
user_chunk = manager.add_message(
    content="How do neural networks work?",
    role="user",
    importance=1.0
)

# Add assistant response
assistant_chunk = manager.add_message(
    content="Neural networks are computational models inspired by biological neurons...",
    role="assistant",
    importance=0.9
)
```

**Process:**
1. Creates unique chunk ID
2. Generates embedding for semantic search
3. Adds to rolling window
4. Stores in semantic index
5. Archives old chunks if window is full
6. Updates statistics

**Returns:** `ContextChunk` object with metadata

#### `get_optimized_context(query: str = None) -> List[Dict[str, str]]`
Gets optimized context combining rolling window and semantic retrieval.

```python
# Get context for a specific query
context = manager.get_optimized_context("Explain backpropagation")

# Get current rolling window context
context = manager.get_optimized_context()

# Context format: [{"role": "user", "content": "..."}, ...]
```

**Optimization Process:**
1. Starts with rolling window (recent context)
2. If query provided, performs semantic search
3. Combines and deduplicates chunks
4. Sorts by relevance and importance
5. Optimizes for token limits
6. Returns formatted messages

#### `get_status() -> Dict[str, Any]`
Gets current system status and statistics.

```python
status = manager.get_status()
print(f"Total chunks: {status['total_chunks']}")
print(f"Rolling window size: {status['rolling_window_size']}")
print(f"Archived chunks: {status['archived_chunks']}")
print(f"LMStudio connected: {status['lmstudio_connected']}")
```

**Returns:**
```python
{
    'total_chunks': int,
    'rolling_window_size': int,
    'archived_chunks': int,
    'semantic_retrievals': int,
    'context_optimizations': int,
    'lmstudio_connected': bool,
    'embedding_model': str,
    'avg_chunk_size': float
}
```

#### `analyze_conversation_patterns() -> Dict[str, Any]`
Analyzes conversation patterns and provides insights.

```python
analysis = manager.analyze_conversation_patterns()
print(f"Total messages: {analysis['total_messages']}")
print(f"Average message length: {analysis['avg_message_length']}")
print(f"Most active role: {analysis['most_active_role']}")
print(f"Topic distribution: {analysis['topic_distribution']}")
```

**Analysis Includes:**
- Message count and length statistics
- Role distribution (user vs assistant)
- Topic clustering and distribution
- Temporal patterns
- Most accessed content
- Conversation quality metrics

#### `export_conversation(filename: str = None) -> str`
Exports conversation history to JSON file.

```python
# Auto-generate filename
filename = manager.export_conversation()
print(f"Exported to: {filename}")

# Custom filename
filename = manager.export_conversation("my_conversation.json")
```

**Export Format:**
```json
{
    "metadata": {
        "export_timestamp": "2025-06-14T10:30:00",
        "total_chunks": 25,
        "conversation_duration": 3600,
        "system_config": {...}
    },
    "conversation": [
        {
            "id": "chunk_id",
            "content": "message content",
            "role": "user",
            "timestamp": 1718360400.0,
            "importance": 1.0,
            "access_count": 3
        }
    ],
    "statistics": {...},
    "analysis": {...}
}
```

### Memory Management Methods

#### `clear_conversation()`
Clears all conversation history and resets the system.

```python
manager.clear_conversation()
print("Conversation cleared")
```

#### `archive_chunk(chunk: ContextChunk)`
Manually archives a specific chunk.

```python
# Archive a specific chunk
manager.archive_chunk(user_chunk)
```

#### `get_archived_chunks() -> List[ContextChunk]`
Retrieves all archived chunks.

```python
archived = manager.get_archived_chunks()
print(f"Found {len(archived)} archived chunks")
```

### Search Methods

#### `search_semantic(query: str, limit: int = 5) -> List[Tuple[ContextChunk, float]]`
Performs semantic search across all stored content.

```python
# Search for relevant content
results = manager.search_semantic("machine learning algorithms", limit=3)

for chunk, similarity in results:
    print(f"Similarity: {similarity:.3f}")
    print(f"Content: {chunk.content[:100]}...")
```

#### `search_keyword(keywords: List[str], limit: int = 10) -> List[ContextChunk]`
Performs keyword-based search.

```python
# Search by keywords
results = manager.search_keyword(["neural", "network", "training"])

for chunk in results:
    print(f"Found: {chunk.content[:50]}...")
```

### Private Methods

#### `_archive_if_needed()`
Automatically archives chunks based on importance threshold.

#### `_calculate_token_count(content: str) -> int`
Estimates token count for content.

#### `_optimize_for_tokens(chunks: List[ContextChunk]) -> List[ContextChunk]`
Optimizes chunk selection to fit within token limits.

## 📦 ContextChunk Class (Traditional)

Represents a single piece of conversation context.

### Constructor

```python
ContextChunk(
    id: str,
    content: str,
    timestamp: float,
    role: str,
    importance: float = 1.0,
    embedding: Optional[List[float]] = None,
    access_count: int = 0,
    last_accessed: float = 0.0
)
```

### Properties

- **id**: Unique identifier (SHA-256 hash)
- **content**: Message content
- **timestamp**: Unix timestamp of creation
- **role**: Message role ("user", "assistant", "system")
- **importance**: Importance score (0.0-1.0)
- **embedding**: Vector embedding for semantic search
- **access_count**: Number of times accessed
- **last_accessed**: Last access timestamp

### Methods

#### `update_access()`
Updates access count and timestamp.

```python
chunk.update_access()
print(f"Access count: {chunk.access_count}")
```

#### `calculate_age() -> float`
Calculates age in seconds since creation.

```python
age = chunk.calculate_age()
print(f"Chunk age: {age:.1f} seconds")
```

## 🔍 SemanticIndex Class

Manages vector embeddings and semantic search functionality.

### Constructor

```python
SemanticIndex()
```

### Methods

#### `store_embedding(chunk: ContextChunk)`
Stores chunk embedding in the index.

```python
semantic_index = SemanticIndex()
semantic_index.store_embedding(chunk)
```

#### `search_similar(query_embedding: List[float], limit: int = 5) -> List[Tuple[str, float]]`
Searches for similar embeddings.

```python
# Get query embedding first
query_embedding = lm_client.get_embedding("machine learning")

# Search for similar content
results = semantic_index.search_similar(query_embedding, limit=3)

for chunk_id, similarity in results:
    print(f"Chunk {chunk_id}: {similarity:.3f} similarity")
```

#### `get_embedding(chunk_id: str) -> Optional[List[float]]`
Retrieves embedding for a specific chunk.

```python
embedding = semantic_index.get_embedding(chunk_id)
if embedding:
    print(f"Embedding dimension: {len(embedding)}")
```

## 🚀 LMStudioClient Class

Handles communication with LMStudio API.

### Constructor

```python
LMStudioClient(
    base_url: str = "http://localhost:1234",
    embedding_model: str = None
)
```

### Methods

#### `get_embedding(text: str) -> Optional[List[float]]`
Gets text embedding from LMStudio.

```python
client = LMStudioClient()
embedding = client.get_embedding("Hello world")
if embedding:
    print(f"Embedding size: {len(embedding)}")
```

#### `chat_completion(messages: List[Dict], model: str, **kwargs) -> Dict`
Sends chat completion request to LMStudio.

```python
messages = [
    {"role": "user", "content": "What is AI?"}
]

response = client.chat_completion(
    messages=messages,
    model="llama-2-7b-chat",
    temperature=0.7,
    max_tokens=500
)

print(response["choices"][0]["message"]["content"])
```

#### `list_models() -> List[str]`
Lists available models in LMStudio.

```python
models = client.list_models()
print(f"Available models: {models}")
```

#### `test_connection() -> bool`
Tests connection to LMStudio.

```python
if client.test_connection():
    print("LMStudio connected successfully")
else:
    print("Failed to connect to LMStudio")
```

## 🎭 ConversationSimulator Class

Simulates realistic conversations for testing and demonstration.

### Constructor

```python
ConversationSimulator(context_manager: ContextWindowManager)
```

### Methods

#### `simulate_conversation(topic: str, num_exchanges: int = 5) -> List[ContextChunk]`
Simulates a conversation on a given topic.

```python
simulator = ConversationSimulator(manager)
chunks = simulator.simulate_conversation("artificial intelligence", num_exchanges=3)

for chunk in chunks:
    print(f"{chunk.role}: {chunk.content}")
```

#### `generate_realistic_response(context: List[ContextChunk]) -> str`
Generates realistic responses based on context.

```python
response = simulator.generate_realistic_response(recent_chunks)
print(f"Generated response: {response}")
```

## 💡 Usage Examples

### Basic Context Management

```python
from context_manager import ContextWindowManager

# Initialize manager
manager = ContextWindowManager(
    max_window_size=4096,
    rolling_window_size=8,
    lmstudio_url="http://localhost:1234"
)

# Add conversation
manager.add_message("What is machine learning?", "user")
manager.add_message("Machine learning is a subset of AI...", "assistant")
manager.add_message("Can you give me an example?", "user")

# Get optimized context
context = manager.get_optimized_context("Show me a neural network example")
print(f"Context has {len(context)} messages")
```

### Semantic Search

```python
# Add multiple messages on different topics
manager.add_message("Python is a programming language", "assistant")
manager.add_message("Neural networks use backpropagation", "assistant")
manager.add_message("Quantum computing uses qubits", "assistant")

# Search for programming-related content
results = manager.search_semantic("programming languages", limit=2)

for chunk, similarity in results:
    print(f"Similarity: {similarity:.3f}")
    print(f"Content: {chunk.content}")
```

### Conversation Analysis

```python
# Simulate a longer conversation
simulator = ConversationSimulator(manager)
simulator.simulate_conversation("deep learning", num_exchanges=10)

# Analyze patterns
analysis = manager.analyze_conversation_patterns()
print(f"Total messages: {analysis['total_messages']}")
print(f"Average length: {analysis['avg_message_length']:.1f} characters")
print(f"Most active role: {analysis['most_active_role']}")

# Export for later analysis
filename = manager.export_conversation()
print(f"Conversation saved to: {filename}")
```

### Integration with Custom LLM

```python
def chat_with_custom_llm(user_input: str) -> str:
    # Add user message
    manager.add_message(user_input, "user")
    
    # Get optimized context
    context = manager.get_optimized_context(user_input)
    
    # Send to your custom LLM
    response = your_llm_function(context)
    
    # Store response
    manager.add_message(response, "assistant")
    
    return response

# Use the function
response = chat_with_custom_llm("Explain quantum entanglement")
print(response)
```

---

**Next**: Explore the [LMStudio Integration API](./lmstudio-integration.md) for advanced LLM integration.
