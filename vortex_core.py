"""
VORTEX: Consciousness-Aware Context Management System
A revolutionary approach to LLM context that creates emergent intelligence patterns
through multi-dimensional awareness, cognitive modeling, and self-evolving memory
"""

import json
import time
import numpy as np
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any, Set
from dataclasses import dataclass, asdict, field
from collections import deque, defaultdict
from enum import Enum
import sqlite3
import hashlib
import requests
from pathlib import Path
import threading
import pickle
from concurrent.futures import ThreadPoolExecutor
import uuid
from abc import ABC, abstractmethod


class CognitionType(Enum):
    """Types of cognitive processes"""
    ANALYTICAL = "analytical"
    CREATIVE = "creative"
    EMOTIONAL = "emotional"
    LOGICAL = "logical"
    INTUITIVE = "intuitive"
    MEMORY_RECALL = "memory_recall"
    PATTERN_RECOGNITION = "pattern_recognition"
    SYNTHESIS = "synthesis"
    REFLECTION = "reflection"


class ContextRelevance(Enum):
    """Context relevance levels"""
    CRITICAL = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    MINIMAL = 1


class ConsciousnessState(Enum):
    """States of consciousness simulation"""
    FOCUSED = "focused"
    EXPLORATORY = "exploratory"
    REFLECTIVE = "reflective"
    CREATIVE = "creative"
    ANALYTICAL = "analytical"
    CONVERSATIONAL = "conversational"


@dataclass
class CognitiveSignature:
    """Represents the cognitive 'fingerprint' of a context chunk"""
    emotion_valence: float = 0.0  # -1 to 1
    complexity_level: float = 0.0  # 0 to 1
    abstraction_level: float = 0.0  # 0 to 1
    creativity_index: float = 0.0  # 0 to 1
    logical_coherence: float = 0.0  # 0 to 1
    temporal_urgency: float = 0.0  # 0 to 1
    cognitive_load: float = 0.0  # 0 to 1
    
    def similarity(self, other: 'CognitiveSignature') -> float:
        """Calculate cognitive similarity between signatures"""
        metrics = [
            self.emotion_valence - other.emotion_valence,
            self.complexity_level - other.complexity_level,
            self.abstraction_level - other.abstraction_level,
            self.creativity_index - other.creativity_index,
            self.logical_coherence - other.logical_coherence,
            self.temporal_urgency - other.temporal_urgency,
            self.cognitive_load - other.cognitive_load
        ]
        distance = np.sqrt(sum(m**2 for m in metrics))
        return max(0, 1 - (distance / np.sqrt(7)))  # Normalize to [0,1]


@dataclass
class ContextChunk:
    """Enhanced context chunk with cognitive awareness"""
    id: str
    content: str
    timestamp: float
    role: str
    
    # Enhanced metadata
    embedding: Optional[List[float]] = None
    cognitive_signature: Optional[CognitiveSignature] = None
    importance: float = 1.0
    access_count: int = 0
    last_accessed: float = 0.0
    
    # Semantic enrichment
    semantic_tags: Set[str] = field(default_factory=set)
    concepts: Set[str] = field(default_factory=set)
    emotions: Dict[str, float] = field(default_factory=dict)
    
    # Relationship mapping
    related_chunks: Dict[str, float] = field(default_factory=dict)  # chunk_id -> similarity
    causal_relationships: Dict[str, str] = field(default_factory=dict)  # chunk_id -> relationship_type
    
    # Evolution tracking
    modification_history: List[Dict] = field(default_factory=list)
    synthesis_products: List[str] = field(default_factory=list)  # chunks created from this one
    
    def __post_init__(self):
        if self.last_accessed == 0.0:
            self.last_accessed = self.timestamp
        if self.cognitive_signature is None:
            self.cognitive_signature = CognitiveSignature()


class NeuroCore:
    """
    The 'brain' of the Vortex system - handles cognitive pattern recognition,
    consciousness state simulation, and emergent intelligence
    """
    
    def __init__(self):
        self.consciousness_state = ConsciousnessState.CONVERSATIONAL
        self.cognitive_patterns = defaultdict(list)
        self.learning_rate = 0.1
        self.attention_focus = {}
        self.working_memory = deque(maxlen=7)  # Miller's magic number
        
        # Neural-inspired components
        self.pattern_weights = defaultdict(float)
        self.concept_network = defaultdict(set)
        self.emotion_state = {"valence": 0.0, "arousal": 0.0, "dominance": 0.0}
        
        # Metacognitive awareness
        self.self_monitoring = {
            "confidence_level": 0.5,
            "cognitive_load": 0.0,
            "learning_momentum": 0.0,
            "attention_stability": 1.0
        }
    
    def analyze_cognitive_signature(self, content: str, role: str, context: List[ContextChunk]) -> CognitiveSignature:
        """Generate a cognitive signature for content"""
        signature = CognitiveSignature()
        
        # Emotion analysis (simplified)
        emotion_keywords = {
            'positive': ['good', 'great', 'excellent', 'amazing', 'wonderful', 'love', 'joy', 'happy'],
            'negative': ['bad', 'terrible', 'awful', 'hate', 'sad', 'angry', 'frustrated', 'worried']
        }
        
        words = content.lower().split()
        positive_count = sum(1 for word in words if word in emotion_keywords['positive'])
        negative_count = sum(1 for word in words if word in emotion_keywords['negative'])
        
        if positive_count + negative_count > 0:
            signature.emotion_valence = (positive_count - negative_count) / (positive_count + negative_count)
        
        # Complexity analysis
        avg_word_length = np.mean([len(word) for word in words]) if words else 0
        unique_words = len(set(words))
        total_words = len(words)
        
        signature.complexity_level = min(1.0, (avg_word_length / 10 + unique_words / total_words) / 2)
        
        # Abstraction level (based on abstract vs concrete words)
        abstract_indicators = ['concept', 'idea', 'theory', 'principle', 'framework', 'paradigm', 'philosophy']
        concrete_indicators = ['see', 'touch', 'hear', 'specific', 'example', 'instance', 'particular']
        
        abstract_score = sum(1 for word in words if word in abstract_indicators)
        concrete_score = sum(1 for word in words if word in concrete_indicators)
        
        if abstract_score + concrete_score > 0:
            signature.abstraction_level = abstract_score / (abstract_score + concrete_score)
        else:
            signature.abstraction_level = 0.5
        
        # Creativity index
        creative_indicators = ['creative', 'innovative', 'imagine', 'invent', 'original', 'unique', 'novel']
        signature.creativity_index = min(1.0, sum(1 for word in words if word in creative_indicators) / 10)
        
        # Logical coherence (simplified)
        logical_indicators = ['because', 'therefore', 'thus', 'consequently', 'since', 'if', 'then']
        signature.logical_coherence = min(1.0, sum(1 for word in words if word in logical_indicators) / 5)
        
        # Temporal urgency
        urgent_indicators = ['urgent', 'immediate', 'quickly', 'asap', 'deadline', 'rush', 'emergency']
        signature.temporal_urgency = min(1.0, sum(1 for word in words if word in urgent_indicators) / 3)
        
        # Cognitive load estimation
        signature.cognitive_load = min(1.0, len(words) / 100 + signature.complexity_level / 2)
        
        return signature
    
    def update_consciousness_state(self, recent_chunks: List[ContextChunk]):
        """Update consciousness state based on recent context"""
        if not recent_chunks:
            return
        
        # Analyze recent cognitive signatures
        signatures = [chunk.cognitive_signature for chunk in recent_chunks if chunk.cognitive_signature]
        
        if not signatures:
            return
        
        avg_creativity = np.mean([s.creativity_index for s in signatures])
        avg_complexity = np.mean([s.complexity_level for s in signatures])
        avg_emotion = np.mean([s.emotion_valence for s in signatures])
        
        # Determine consciousness state
        if avg_creativity > 0.7:
            self.consciousness_state = ConsciousnessState.CREATIVE
        elif avg_complexity > 0.8:
            self.consciousness_state = ConsciousnessState.ANALYTICAL
        elif abs(avg_emotion) > 0.5:
            self.consciousness_state = ConsciousnessState.REFLECTIVE
        elif avg_complexity > 0.6:
            self.consciousness_state = ConsciousnessState.FOCUSED
        else:
            self.consciousness_state = ConsciousnessState.CONVERSATIONAL
    
    def generate_insights(self, chunks: List[ContextChunk]) -> List[Dict]:
        """Generate insights from context patterns"""
        insights = []
        
        if len(chunks) < 3:
            return insights
        
        # Pattern detection
        cognitive_evolution = []
        for chunk in chunks[-10:]:  # Last 10 chunks
            if chunk.cognitive_signature:
                cognitive_evolution.append({
                    'timestamp': chunk.timestamp,
                    'complexity': chunk.cognitive_signature.complexity_level,
                    'creativity': chunk.cognitive_signature.creativity_index,
                    'emotion': chunk.cognitive_signature.emotion_valence
                })
        
        if len(cognitive_evolution) >= 3:
            complexity_trend = np.polyfit(
                range(len(cognitive_evolution)), 
                [c['complexity'] for c in cognitive_evolution], 
                1
            )[0]
            
            if complexity_trend > 0.1:
                insights.append({
                    'type': 'cognitive_evolution',
                    'message': 'Conversation complexity is increasing - entering deeper analytical territory',
                    'confidence': min(1.0, abs(complexity_trend) * 5)
                })
            elif complexity_trend < -0.1:
                insights.append({
                    'type': 'cognitive_evolution',
                    'message': 'Conversation simplifying - moving toward practical application',
                    'confidence': min(1.0, abs(complexity_trend) * 5)
                })
        
        return insights


class VortexCore:
    """
    Main Vortex system - consciousness-aware context management
    """
    
    def __init__(self,
                 max_context_tokens: int = 8000,
                 working_memory_size: int = 7,
                 lmstudio_url: str = "http://localhost:1234",
                 embedding_model: str = None,
                 db_path: str = "vortex_consciousness.db"):
        
        self.max_context_tokens = max_context_tokens
        self.working_memory_size = working_memory_size
        self.lmstudio_url = lmstudio_url
        self.embedding_model = embedding_model
        self.db_path = db_path
        
        # Core components
        self.neuro_core = NeuroCore()
        self.session = requests.Session()
        
        # Storage systems
        self.working_memory = deque(maxlen=working_memory_size)
        self.episodic_memory = {}  # Long-term storage
        self.semantic_network = defaultdict(set)  # Concept relationships
        
        # Consciousness simulation
        self.consciousness_thread = None
        self.running = False
        
        # Initialize database
        self.init_consciousness_database()
        
        # Statistics and monitoring
        self.stats = {
            'total_chunks_processed': 0,
            'consciousness_state_changes': 0,
            'insights_generated': 0,
            'pattern_recognitions': 0,
            'synthesis_operations': 0
        }
    
    def init_consciousness_database(self):
        """Initialize the consciousness database with advanced schemas"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Enhanced embeddings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS context_chunks (
                chunk_id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                role TEXT NOT NULL,
                timestamp REAL NOT NULL,
                embedding BLOB,
                cognitive_signature BLOB,
                importance REAL DEFAULT 1.0,
                access_count INTEGER DEFAULT 0,
                last_accessed REAL,
                semantic_tags TEXT,
                concepts TEXT,
                emotions TEXT
            )
        ''')
        
        # Relationship mapping
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chunk_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chunk_a TEXT NOT NULL,
                chunk_b TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                strength REAL NOT NULL,
                created_at REAL NOT NULL,
                FOREIGN KEY (chunk_a) REFERENCES context_chunks (chunk_id),
                FOREIGN KEY (chunk_b) REFERENCES context_chunks (chunk_id)
            )
        ''')
        
        # Consciousness state log
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS consciousness_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp REAL NOT NULL,
                state TEXT NOT NULL,
                confidence REAL,
                cognitive_load REAL,
                insights TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def start_consciousness_simulation(self):
        """Start the background consciousness simulation"""
        if self.consciousness_thread and self.consciousness_thread.is_alive():
            return
        
        self.running = True
        self.consciousness_thread = threading.Thread(target=self._consciousness_loop, daemon=True)
        self.consciousness_thread.start()
    
    def _consciousness_loop(self):
        """Background consciousness simulation loop"""
        while self.running:
            try:
                # Update consciousness state
                recent_chunks = list(self.working_memory)
                self.neuro_core.update_consciousness_state(recent_chunks)
                
                # Generate insights
                if len(recent_chunks) >= 3:
                    insights = self.neuro_core.generate_insights(recent_chunks)
                    if insights:
                        self.stats['insights_generated'] += len(insights)
                        self._log_consciousness_state(insights)
                
                # Sleep for a bit
                time.sleep(2)
                
            except Exception as e:
                print(f"Consciousness loop error: {e}")
                time.sleep(5)
    
    def add_context(self, content: str, role: str = "user") -> ContextChunk:
        """Add context with full cognitive analysis"""
        chunk_id = str(uuid.uuid4())
        timestamp = time.time()
        
        # Create context chunk
        chunk = ContextChunk(
            id=chunk_id,
            content=content,
            timestamp=timestamp,
            role=role
        )
        
        # Generate cognitive signature
        recent_context = list(self.working_memory)
        chunk.cognitive_signature = self.neuro_core.analyze_cognitive_signature(
            content, role, recent_context
        )
        
        # Generate embedding
        chunk.embedding = self._get_embedding(content)
        
        # Add to working memory
        self.working_memory.append(chunk)
        
        # Store in database
        self._store_chunk(chunk)
        
        # Update statistics
        self.stats['total_chunks_processed'] += 1
        
        return chunk
    
    def get_optimized_context(self, query: str = None) -> List[Dict[str, str]]:
        """Get consciousness-optimized context for the current query"""
        # Start with working memory
        context_chunks = list(self.working_memory)
        
        # If we have a specific query, find semantically relevant chunks
        if query:
            query_embedding = self._get_embedding(query)
            if query_embedding:
                relevant_chunks = self._find_semantically_relevant(query_embedding, limit=5)
                context_chunks.extend(relevant_chunks)
        
        # Remove duplicates while preserving order
        seen_ids = set()
        unique_chunks = []
        for chunk in context_chunks:
            if chunk.id not in seen_ids:
                unique_chunks.append(chunk)
                seen_ids.add(chunk.id)
        
        # Sort by relevance and importance
        unique_chunks.sort(key=lambda c: (c.importance, c.timestamp), reverse=True)
        
        # Convert to message format
        messages = []
        for chunk in unique_chunks:
            messages.append({
                "role": chunk.role,
                "content": chunk.content
            })
        
        return messages
    
    def _get_embedding(self, text: str) -> Optional[List[float]]:
        """Get embedding for text"""
        try:
            response = self.session.post(
                f"{self.lmstudio_url}/v1/embeddings",
                json={
                    "input": text,
                    "model": self.embedding_model or "text-embedding-ada-002"
                },
                timeout=30
            )
            if response.status_code == 200:
                return response.json()["data"][0]["embedding"]
        except Exception as e:
            print(f"Embedding error: {e}")
        
        # Fallback to pseudo-embedding
        return self._pseudo_embedding(text)
    
    def _pseudo_embedding(self, text: str, dim: int = 768) -> List[float]:
        """Generate pseudo-embedding from text hash"""
        hash_obj = hashlib.md5(text.encode())
        np.random.seed(int(hash_obj.hexdigest(), 16) % (2**32))
        return np.random.randn(dim).tolist()
    
    def _store_chunk(self, chunk: ContextChunk):
        """Store chunk in consciousness database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Serialize complex data
        embedding_blob = np.array(chunk.embedding).tobytes() if chunk.embedding else None
        cognitive_blob = pickle.dumps(chunk.cognitive_signature) if chunk.cognitive_signature else None
        semantic_tags = json.dumps(list(chunk.semantic_tags))
        concepts = json.dumps(list(chunk.concepts))
        emotions = json.dumps(chunk.emotions)
        
        cursor.execute('''
            INSERT OR REPLACE INTO context_chunks
            (chunk_id, content, role, timestamp, embedding, cognitive_signature,
             importance, access_count, last_accessed, semantic_tags, concepts, emotions)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            chunk.id, chunk.content, chunk.role, chunk.timestamp,
            embedding_blob, cognitive_blob, chunk.importance,
            chunk.access_count, chunk.last_accessed,
            semantic_tags, concepts, emotions
        ))
        
        conn.commit()
        conn.close()
    
    def _find_semantically_relevant(self, query_embedding: List[float], limit: int = 5) -> List[ContextChunk]:
        """Find semantically relevant chunks from episodic memory"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM context_chunks WHERE embedding IS NOT NULL')
        results = []
        query_vec = np.array(query_embedding)
        
        for row in cursor.fetchall():
            chunk_id, content, role, timestamp, embedding_blob, cognitive_blob, \
            importance, access_count, last_accessed, semantic_tags, concepts, emotions = row
            
            if embedding_blob:
                stored_vec = np.frombuffer(embedding_blob, dtype=np.float64)
                similarity = self._cosine_similarity(query_vec, stored_vec)
                
                # Create chunk object
                chunk = ContextChunk(
                    id=chunk_id,
                    content=content,
                    role=role,
                    timestamp=timestamp,
                    importance=importance,
                    access_count=access_count,
                    last_accessed=last_accessed
                )
                
                if cognitive_blob:
                    chunk.cognitive_signature = pickle.loads(cognitive_blob)
                
                chunk.semantic_tags = set(json.loads(semantic_tags) if semantic_tags else [])
                chunk.concepts = set(json.loads(concepts) if concepts else [])
                chunk.emotions = json.loads(emotions) if emotions else {}
                
                results.append((chunk, similarity))
        
        conn.close()
        
        # Sort by similarity and return top chunks
        results.sort(key=lambda x: x[1], reverse=True)
        return [chunk for chunk, _ in results[:limit]]
    
    def _cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
        """Calculate cosine similarity"""
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    
    def _log_consciousness_state(self, insights: List[Dict]):
        """Log consciousness state and insights"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO consciousness_log
            (timestamp, state, confidence, cognitive_load, insights)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            time.time(),
            self.neuro_core.consciousness_state.value,
            self.neuro_core.self_monitoring['confidence_level'],
            self.neuro_core.self_monitoring['cognitive_load'],
            json.dumps(insights)
        ))
        
        conn.commit()
        conn.close()
    
    def get_consciousness_report(self) -> Dict[str, Any]:
        """Get detailed consciousness state report"""
        return {
            'current_state': self.neuro_core.consciousness_state.value,
            'self_monitoring': self.neuro_core.self_monitoring.copy(),
            'emotion_state': self.neuro_core.emotion_state.copy(),
            'working_memory_size': len(self.working_memory),
            'statistics': self.stats.copy(),
            'consciousness_active': self.running and self.consciousness_thread and self.consciousness_thread.is_alive()
        }
    
    def shutdown(self):
        """Gracefully shutdown the consciousness system"""
        self.running = False
        if self.consciousness_thread and self.consciousness_thread.is_alive():
            self.consciousness_thread.join(timeout=5) 