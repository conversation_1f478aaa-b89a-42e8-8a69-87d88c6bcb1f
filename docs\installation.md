# 🛠️ Installation Guide

Complete installation instructions for VORTEX Consciousness-Aware Context Management System.

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **RAM**: 4GB (8GB recommended for consciousness simulation)
- **Storage**: 1GB free space
- **OS**: Windows 10/11, macOS 10.14+, Linux (Ubuntu 18.04+)

### Recommended Requirements
- **Python**: 3.10 or higher
- **RAM**: 16GB (for large context windows and complex simulations)
- **Storage**: 5GB free space (for conversation archives and models)
- **GPU**: Optional, for faster embedding generation
- **OS**: Latest stable versions

### External Dependencies
- **LMStudio**: For local LLM integration (recommended)
- **Internet Connection**: For initial setup and model downloads

## 🚀 Quick Installation

### Option 1: Standard Installation

```bash
# Clone the repository
git clone https://github.com/your-username/vortex.git
cd vortex

# Install Python dependencies
pip install -r requirements_context.txt

# Verify installation
python -c "from vortex_core import VortexCore; print('VORTEX installed successfully!')"
```

### Option 2: Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv vortex_env

# Activate virtual environment
# On Windows:
vortex_env\Scripts\activate
# On macOS/Linux:
source vortex_env/bin/activate

# Install dependencies
pip install -r requirements_context.txt

# Test installation
python demo_context_manager.py
```

### Option 3: Conda Environment

```bash
# Create conda environment
conda create -n vortex python=3.10
conda activate vortex

# Install dependencies
pip install -r requirements_context.txt

# Optional: Install additional scientific packages
conda install numpy scipy matplotlib
```

## 📦 Dependency Details

### Core Dependencies

```bash
# Essential packages (automatically installed)
numpy>=1.21.0          # Numerical computing
requests>=2.28.0       # HTTP requests for LMStudio API
```

### Built-in Python Modules
These are included with Python and don't need separate installation:
- `sqlite3` - Database storage
- `dataclasses` - Data structures (Python 3.7+)
- `typing` - Type hints (Python 3.5+)
- `hashlib` - Cryptographic hashing
- `collections` - Specialized container datatypes
- `json` - JSON data handling
- `time` - Time-related functions
- `datetime` - Date and time handling
- `os` - Operating system interface

### Optional Dependencies

```bash
# For advanced features
pip install scipy>=1.9.0        # Advanced similarity calculations
pip install matplotlib>=3.5.0   # Visualization
pip install plotly>=5.0.0       # Interactive visualizations
pip install flask>=2.0.0        # Web interface
pip install streamlit>=1.25.0   # Alternative web interface
```

### Space Simulation Dependencies

```bash
# For physics simulation components
pip install matplotlib>=3.5.0   # 3D plotting
pip install tkinter             # GUI (usually included with Python)
```

## 🔧 LMStudio Setup

### Installing LMStudio

1. **Download LMStudio**
   - Visit [https://lmstudio.ai/](https://lmstudio.ai/)
   - Download for your operating system
   - Install following the official instructions

2. **Download Models**
   ```bash
   # Recommended models for VORTEX:
   # - Llama 2 7B Chat (good balance of speed/quality)
   # - Mistral 7B Instruct (excellent instruction following)
   # - CodeLlama 7B (for programming assistance)
   # - Any embedding model (for semantic search)
   ```

3. **Configure API Server**
   - Launch LMStudio
   - Go to "Local Server" tab
   - Load your preferred model
   - Start the server (default: http://localhost:1234)
   - Enable CORS if needed for web interface

### Testing LMStudio Connection

```bash
# Test if LMStudio is running
curl http://localhost:1234/v1/models

# Expected response: JSON list of available models
```

```python
# Test from Python
from context_manager import ContextWindowManager

manager = ContextWindowManager(lmstudio_url="http://localhost:1234")
status = manager.get_status()
print(f"LMStudio connected: {status['lmstudio_connected']}")
```

## 🧪 Verification & Testing

### Basic Functionality Test

```python
# test_installation.py
from vortex_core import VortexCore
from context_manager import ContextWindowManager
import time

def test_basic_functionality():
    print("🧪 Testing VORTEX Installation...")
    
    # Test 1: Basic Context Manager
    print("1. Testing Context Manager...")
    manager = ContextWindowManager()
    manager.add_message("Hello, world!", "user")
    context = manager.get_optimized_context()
    assert len(context) == 1
    print("   ✅ Context Manager working")
    
    # Test 2: VORTEX Core
    print("2. Testing VORTEX Core...")
    vortex = VortexCore()
    chunk = vortex.add_context("Test consciousness", "user")
    assert chunk.cognitive_signature is not None
    print("   ✅ VORTEX Core working")
    
    # Test 3: Consciousness Simulation
    print("3. Testing Consciousness Simulation...")
    vortex.start_consciousness_simulation()
    time.sleep(1)
    report = vortex.get_consciousness_report()
    assert report['consciousness_active'] == True
    vortex.shutdown()
    print("   ✅ Consciousness Simulation working")
    
    print("🎉 All tests passed! VORTEX is ready to use.")

if __name__ == "__main__":
    test_basic_functionality()
```

### Run the Test

```bash
python test_installation.py
```

### Expected Output

```
🧪 Testing VORTEX Installation...
1. Testing Context Manager...
   ✅ Context Manager working
2. Testing VORTEX Core...
   ✅ VORTEX Core working
3. Testing Consciousness Simulation...
   ✅ Consciousness Simulation working
🎉 All tests passed! VORTEX is ready to use.
```

## 🌐 Web Interface Setup

### Flask Web Interface

```bash
# Install Flask if not already installed
pip install flask>=2.0.0

# Start the web interface
python web_interface.py

# Open browser to http://localhost:5000
```

### Streamlit Alternative (Optional)

```bash
# Install Streamlit
pip install streamlit>=1.25.0

# Create streamlit app (if available)
streamlit run streamlit_app.py
```

## 🐳 Docker Installation (Advanced)

### Dockerfile

```dockerfile
FROM python:3.10-slim

WORKDIR /app

# Copy requirements
COPY requirements_context.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements_context.txt

# Copy application
COPY . .

# Expose port for web interface
EXPOSE 5000

# Default command
CMD ["python", "web_interface.py"]
```

### Docker Commands

```bash
# Build image
docker build -t vortex .

# Run container
docker run -p 5000:5000 -p 1234:1234 vortex

# Run with volume for persistence
docker run -p 5000:5000 -v $(pwd)/data:/app/data vortex
```

## 🔧 Configuration

### Environment Variables

```bash
# Optional environment variables
export VORTEX_LMSTUDIO_URL="http://localhost:1234"
export VORTEX_DB_PATH="./vortex_consciousness.db"
export VORTEX_MAX_TOKENS="8000"
export VORTEX_WORKING_MEMORY_SIZE="7"
```

### Configuration File

Create `config.json`:

```json
{
    "lmstudio_url": "http://localhost:1234",
    "max_context_tokens": 8000,
    "working_memory_size": 7,
    "embedding_model": "text-embedding-ada-002",
    "db_path": "./vortex_consciousness.db",
    "consciousness_simulation": true,
    "web_interface": {
        "host": "0.0.0.0",
        "port": 5000,
        "debug": false
    }
}
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Import Errors

```bash
# Error: ModuleNotFoundError: No module named 'numpy'
pip install numpy>=1.21.0

# Error: ModuleNotFoundError: No module named 'vortex_core'
# Make sure you're in the correct directory
cd /path/to/vortex
python -c "import vortex_core"
```

#### 2. LMStudio Connection Issues

```bash
# Error: Connection refused
# 1. Check if LMStudio is running
curl http://localhost:1234/v1/models

# 2. Check if port is correct
netstat -an | grep 1234

# 3. Try different URL
export VORTEX_LMSTUDIO_URL="http://127.0.0.1:1234"
```

#### 3. Memory Issues

```python
# Reduce memory usage
vortex = VortexCore(
    max_context_tokens=2000,  # Reduce from default 8000
    working_memory_size=5     # Reduce from default 7
)
```

#### 4. Database Issues

```bash
# Error: Database locked
# Remove existing database
rm vortex_consciousness.db

# Or use different path
export VORTEX_DB_PATH="./new_vortex.db"
```

### Performance Optimization

```python
# For better performance
import os
os.environ['PYTHONUNBUFFERED'] = '1'  # Better logging
os.environ['NUMBA_DISABLE_JIT'] = '1'  # If using numba

# Optimize for your system
vortex = VortexCore(
    max_context_tokens=4096,    # Adjust based on your model
    working_memory_size=5,      # Reduce if memory constrained
)
```

## ✅ Post-Installation Steps

1. **Run the Demo**
   ```bash
   python demo_context_manager.py
   ```

2. **Try the Web Interface**
   ```bash
   python web_interface.py
   # Open http://localhost:5000
   ```

3. **Read the Documentation**
   - [Quick Start Guide](./quick-start.md)
   - [Basic Usage Tutorial](./tutorials/basic-usage.md)
   - [API Reference](./api/)

4. **Join the Community**
   - Check the GitHub repository for updates
   - Report issues and contribute improvements
   - Share your use cases and experiences

## 🆘 Getting Help

- **Documentation**: Check the [docs/](./README.md) folder
- **Troubleshooting**: See [troubleshooting/common-issues.md](./troubleshooting/common-issues.md)
- **GitHub Issues**: Report bugs and request features
- **Community**: Join discussions and share experiences

---

**Next**: Continue with the [Quick Start Guide](./quick-start.md) to begin using VORTEX!
