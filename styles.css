* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow: hidden;
}

#container {
    display: flex;
    height: 100vh;
}

#canvas-container {
    flex: 1;
    position: relative;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
}

#simulation-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

#controls-panel {
    width: 320px;
    background: rgba(20, 20, 30, 0.95);
    backdrop-filter: blur(10px);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
    overflow-y: auto;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
}

#controls-panel h2 {
    color: #64b5f6;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5em;
    text-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
}

.control-section {
    margin-bottom: 25px;
    padding: 15px;
    background: rgba(30, 30, 40, 0.6);
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.control-section h3 {
    color: #81c784;
    margin-bottom: 15px;
    font-size: 1.1em;
    border-bottom: 1px solid rgba(129, 199, 132, 0.3);
    padding-bottom: 5px;
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    margin-bottom: 5px;
    color: #e0e0e0;
    font-size: 0.9em;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    outline: none;
    margin-bottom: 5px;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background: #64b5f6;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #64b5f6;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
}

.control-group span {
    color: #ffb74d;
    font-weight: bold;
    font-size: 0.9em;
}

.control-group button {
    background: linear-gradient(135deg, #64b5f6, #42a5f5);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    margin-right: 10px;
    margin-bottom: 5px;
    font-size: 0.9em;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(100, 181, 246, 0.3);
}

.control-group button:hover {
    background: linear-gradient(135deg, #42a5f5, #1e88e5);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(100, 181, 246, 0.4);
}

.control-group button:active {
    transform: translateY(0);
}

#addSun {
    background: linear-gradient(135deg, #ffb74d, #ff9800);
}

#addSun:hover {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

#addPlanet {
    background: linear-gradient(135deg, #81c784, #4caf50);
}

#addPlanet:hover {
    background: linear-gradient(135deg, #4caf50, #388e3c);
}

.control-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.control-group label input[type="checkbox"] {
    display: inline;
    margin-bottom: 0;
}

#info {
    background: rgba(40, 40, 50, 0.8);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(100, 181, 246, 0.3);
}

#info div {
    margin-bottom: 8px;
    color: #e0e0e0;
    font-size: 0.9em;
}

#info span {
    color: #64b5f6;
    font-weight: bold;
}

.control-group small {
    color: #b0b0b0;
    font-size: 0.8em;
    line-height: 1.4;
}

/* Scrollbar styling */
#controls-panel::-webkit-scrollbar {
    width: 8px;
}

#controls-panel::-webkit-scrollbar-track {
    background: rgba(20, 20, 30, 0.5);
}

#controls-panel::-webkit-scrollbar-thumb {
    background: rgba(100, 181, 246, 0.5);
    border-radius: 4px;
}

#controls-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 181, 246, 0.7);
}

/* Responsive design */
@media (max-width: 768px) {
    #container {
        flex-direction: column;
    }
    
    #controls-panel {
        width: 100%;
        height: 200px;
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    #canvas-container {
        height: calc(100vh - 200px);
    }
}