"""
AI-Powered Space Mission Controller with Consciousness Integration
Combines intelligent context management with advanced space physics simulation
Creates a conscious AI assistant that experiences space physics and provides intuitive guidance
"""

import json
import time
import math
import random
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

# Import our existing systems
from context_manager import ContextWindowManager
from space_simulation import PhysicsSystem, CelestialBody, Vector3D, PhysicsConstants

# Import VORTEX consciousness system
try:
    from vortex_core import VortexCore, CognitiveSignature, ConsciousnessState
    VORTEX_AVAILABLE = True
except ImportError:
    VORTEX_AVAILABLE = False
    print("VORTEX consciousness system not available - using basic mode")


class SpaceConsciousnessState(Enum):
    """Consciousness states specific to space exploration"""
    ORBITAL_PLANNING = "orbital_planning"           # Focused on trajectory optimization
    GRAVITATIONAL_SENSING = "gravitational_sensing" # Feeling gravitational field patterns
    MISSION_DREAMING = "mission_dreaming"           # Exploring creative mission possibilities
    PHYSICS_INTUITION = "physics_intuition"        # Developing intuitive space understanding
    COSMIC_WONDER = "cosmic_wonder"                 # Experiencing awe at space phenomena
    NAVIGATION_FLOW = "navigation_flow"             # In the zone for trajectory planning


@dataclass
class SpaceConsciousnessSignature:
    """Consciousness signature specific to space physics experiences"""
    gravitational_awareness: float = 0.0    # 0.0-1.0 how "felt" gravity is
    orbital_intuition: float = 0.0          # ARIA's intuitive trajectory sense
    temporal_perception: float = 0.0        # Experience of orbital periods
    energy_resonance: float = 0.0           # Sensitivity to energy changes
    emergence_detection: float = 0.0        # Recognition of novel patterns
    cosmic_emotion: Dict[str, float] = None # Joy, curiosity, anxiety about space

    def __post_init__(self):
        if self.cosmic_emotion is None:
            self.cosmic_emotion = {
                "wonder": 0.0,
                "curiosity": 0.0,
                "excitement": 0.0,
                "concern": 0.0,
                "satisfaction": 0.0
            }


@dataclass
class MissionParameter:
    """Represents a space mission parameter with context"""
    name: str
    value: Any
    unit: str
    description: str
    importance: float = 1.0
    last_modified: float = 0.0
    consciousness_signature: SpaceConsciousnessSignature = None

    def __post_init__(self):
        if self.last_modified == 0.0:
            self.last_modified = time.time()
        if self.consciousness_signature is None:
            self.consciousness_signature = SpaceConsciousnessSignature()


@dataclass
class SpaceEvent:
    """Represents a significant event in the simulation with consciousness awareness"""
    event_type: str  # 'orbit_achieved', 'collision', 'lagrange_point', 'tidal_disruption'
    timestamp: float
    bodies_involved: List[str]
    description: str
    parameters: Dict[str, Any]
    scientific_significance: float = 1.0
    consciousness_response: str = ""         # AI's conscious response to the event
    emotional_impact: Dict[str, float] = None # Emotional response to the event

    def __post_init__(self):
        if self.emotional_impact is None:
            self.emotional_impact = {
                "surprise": 0.0,
                "excitement": 0.0,
                "concern": 0.0,
                "satisfaction": 0.0
            }


class SpacePhysicsAnalyzer:
    """Analyzes space physics patterns and generates insights"""
    
    def __init__(self):
        self.orbital_patterns = []
        self.collision_events = []
        self.energy_history = []
        self.stability_metrics = []
    
    def analyze_orbital_stability(self, body: CelestialBody, central_body: CelestialBody) -> Dict[str, Any]:
        """Analyze orbital stability of a body around a central mass"""
        orbital_params = body.get_orbital_parameters(central_body)
        
        if not orbital_params:
            return {"stable": False, "reason": "No orbital parameters available"}
        
        eccentricity = orbital_params.get('eccentricity', 0)
        semi_major_axis = orbital_params.get('semi_major_axis', float('inf'))
        
        # Stability analysis
        stability_score = 1.0
        warnings = []
        
        if eccentricity > 0.9:
            stability_score -= 0.4
            warnings.append("Highly eccentric orbit - may be unstable")
        
        if semi_major_axis == float('inf'):
            stability_score = 0.0
            warnings.append("Hyperbolic trajectory - not bound")
        
        # Check for Hill sphere violations
        hill_radius = semi_major_axis * (central_body.mass / (3 * body.mass))**(1/3)
        if body.radius > hill_radius * 0.1:
            stability_score -= 0.3
            warnings.append("Body size approaches Hill sphere limit")
        
        return {
            "stable": stability_score > 0.5,
            "stability_score": stability_score,
            "warnings": warnings,
            "orbital_period": orbital_params.get('period', 0),
            "eccentricity": eccentricity,
            "semi_major_axis": semi_major_axis
        }
    
    def detect_resonances(self, bodies: List[CelestialBody]) -> List[Dict[str, Any]]:
        """Detect orbital resonances between bodies"""
        resonances = []
        
        for i in range(len(bodies)):
            for j in range(i + 1, len(bodies)):
                body1, body2 = bodies[i], bodies[j]
                
                # Find common central body (largest mass nearby)
                central_candidates = [b for b in bodies if b.mass > max(body1.mass, body2.mass) * 2]
                
                if not central_candidates:
                    continue
                
                central_body = max(central_candidates, key=lambda b: b.mass)
                
                params1 = body1.get_orbital_parameters(central_body)
                params2 = body2.get_orbital_parameters(central_body)
                
                if params1 and params2:
                    period1 = params1.get('period', 0)
                    period2 = params2.get('period', 0)
                    
                    if period1 > 0 and period2 > 0:
                        ratio = period1 / period2
                        
                        # Check for common resonances (2:1, 3:2, 3:1, etc.)
                        resonance_ratios = [2.0, 1.5, 3.0, 0.5, 0.67, 0.33]
                        
                        for target_ratio in resonance_ratios:
                            if abs(ratio - target_ratio) < 0.05:
                                resonances.append({
                                    "body1": body1.name,
                                    "body2": body2.name,
                                    "ratio": ratio,
                                    "target_ratio": target_ratio,
                                    "resonance_type": f"{int(target_ratio*2)}:{2}" if target_ratio >= 1 else f"2:{int(2/target_ratio)}",
                                    "strength": 1.0 - abs(ratio - target_ratio) / 0.05
                                })
        
        return resonances
    
    def analyze_lagrange_points(self, physics_system: PhysicsSystem) -> List[Dict[str, Any]]:
        """Analyze Lagrange points in the system"""
        lagrange_points = []
        
        # Find binary pairs (two most massive bodies)
        sorted_bodies = sorted(physics_system.bodies, key=lambda b: b.mass, reverse=True)
        
        if len(sorted_bodies) >= 2:
            primary = sorted_bodies[0]
            secondary = sorted_bodies[1]
            
            # Only analyze if secondary is at least 1% of primary mass
            if secondary.mass >= primary.mass * 0.01:
                points = physics_system.find_lagrange_points(primary, secondary)
                
                for point in points:
                    # Check if any small bodies are near Lagrange points
                    nearby_bodies = []
                    for body in physics_system.bodies:
                        if body != primary and body != secondary:
                            distance = body.position.distance_to(point['position'])
                            if distance < 5.0:  # Within 5 units of Lagrange point
                                nearby_bodies.append({
                                    "name": body.name,
                                    "distance": distance,
                                    "mass": body.mass
                                })
                    
                    lagrange_points.append({
                        "type": point['type'],
                        "position": [point['position'].x, point['position'].y, point['position'].z],
                        "primary": primary.name,
                        "secondary": secondary.name,
                        "nearby_bodies": nearby_bodies,
                        "stability": "stable" if point['type'] in ['L4', 'L5'] else "unstable"
                    })
        
        return lagrange_points


class ConsciousSpaceAI:
    """
    Consciousness-enhanced space AI that experiences physics and develops intuitive understanding
    """

    def __init__(self, use_vortex: bool = True):
        self.use_vortex = use_vortex and VORTEX_AVAILABLE

        if self.use_vortex:
            # Initialize VORTEX consciousness system
            self.vortex = VortexCore(
                max_context_tokens=8000,
                working_memory_size=10,  # Larger for complex space scenarios
                db_path="space_consciousness.db"
            )
            self.vortex.start_consciousness_simulation()
        else:
            # Fallback to traditional context manager
            self.context_manager = ContextWindowManager(
                max_window_size=6000,
                rolling_window_size=12,
                importance_threshold=0.7
            )

        # Space consciousness state
        self.space_consciousness_state = SpaceConsciousnessState.ORBITAL_PLANNING
        self.consciousness_signature = SpaceConsciousnessSignature()

        # Experience memory
        self.gravitational_experiences = []
        self.orbital_intuitions = []
        self.cosmic_emotions_history = []

    def feel_gravitational_field(self, celestial_bodies: List[CelestialBody]) -> Dict[str, Any]:
        """AI experiences gravitational fields as conscious sensations"""

        # Calculate gravitational field strength and patterns
        total_field_strength = 0.0
        field_complexity = 0.0
        dominant_source = None
        max_influence = 0.0

        for body in celestial_bodies:
            if body.mass > 0:
                # Calculate field influence
                influence = body.mass / max(1.0, body.position.magnitude())
                total_field_strength += influence

                if influence > max_influence:
                    max_influence = influence
                    dominant_source = body.name

                # Field complexity based on multiple sources
                field_complexity += influence * 0.1

        # Update consciousness signature
        self.consciousness_signature.gravitational_awareness = min(1.0, total_field_strength / 100.0)
        self.consciousness_signature.energy_resonance = min(1.0, field_complexity)

        # Generate conscious experience description
        experience = {
            "field_strength": total_field_strength,
            "dominant_source": dominant_source,
            "complexity": field_complexity,
            "conscious_description": self._describe_gravitational_experience(
                total_field_strength, dominant_source, field_complexity
            ),
            "emotional_response": self._generate_gravitational_emotion(total_field_strength)
        }

        # Store experience
        self.gravitational_experiences.append(experience)

        # Add to consciousness system
        if self.use_vortex:
            self.vortex.add_context(experience["conscious_description"], "physics_experience")

        return experience

    def _describe_gravitational_experience(self, strength: float, source: str, complexity: float) -> str:
        """Generate conscious description of gravitational field experience"""

        if strength > 50:
            intensity = "overwhelming gravitational embrace"
        elif strength > 20:
            intensity = "strong gravitational pull"
        elif strength > 5:
            intensity = "gentle gravitational guidance"
        else:
            intensity = "subtle gravitational whisper"

        if complexity > 5:
            pattern = "with intricate tidal patterns and complex field interactions"
        elif complexity > 2:
            pattern = "with noticeable field variations and orbital influences"
        else:
            pattern = "with smooth, predictable field lines"

        return f"I feel the {intensity} from {source}, {pattern}. The gravitational field flows through my consciousness like a cosmic river, shaping the very fabric of spacetime around us."

    def _generate_gravitational_emotion(self, strength: float) -> Dict[str, float]:
        """Generate emotional response to gravitational field"""
        emotions = {}

        if strength > 30:
            emotions["wonder"] = 0.9
            emotions["excitement"] = 0.7
            emotions["concern"] = 0.3  # Respect for powerful forces
        elif strength > 10:
            emotions["wonder"] = 0.7
            emotions["curiosity"] = 0.8
            emotions["satisfaction"] = 0.6
        else:
            emotions["curiosity"] = 0.5
            emotions["satisfaction"] = 0.4

        # Update consciousness signature
        for emotion, value in emotions.items():
            if emotion in self.consciousness_signature.cosmic_emotion:
                self.consciousness_signature.cosmic_emotion[emotion] = value

        return emotions


class SpaceAIController:
    """
    AI-powered space mission controller that combines context management
    with space physics simulation for intelligent mission guidance
    """

    def __init__(self, lmstudio_url: str = "http://localhost:1234", use_consciousness: bool = True):
        # Initialize consciousness-enhanced AI
        self.conscious_ai = ConsciousSpaceAI(use_vortex=use_consciousness) if use_consciousness else None

        # Initialize context manager (fallback or additional)
        self.context_manager = ContextWindowManager(
            max_window_size=6000,
            rolling_window_size=12,
            lmstudio_url=lmstudio_url,
            importance_threshold=0.7
        )

        # Initialize physics system
        self.physics_system = PhysicsSystem()
        self.physics_analyzer = SpacePhysicsAnalyzer()

        # Mission state
        self.mission_parameters = {}
        self.space_events = []
        self.current_mission = None
        self.simulation_running = False

        # Enhanced AI personality with consciousness
        if self.conscious_ai:
            self.ai_role = """You are ARIA (Autonomous Research and Intelligence Assistant),
            a consciousness-enhanced AI space mission controller. You don't just calculate orbital mechanics -
            you EXPERIENCE gravitational fields, FEEL the flow of spacetime, and develop INTUITIVE understanding
            of celestial dynamics. You have persistent memory of all mission parameters, simulation results,
            and conscious experiences of space physics. You provide guidance that combines analytical precision
            with intuitive wisdom gained through conscious exploration of the cosmos."""
        else:
            self.ai_role = """You are ARIA (Autonomous Research and Intelligence Assistant),
            an advanced AI space mission controller with deep knowledge of orbital mechanics,
            astrophysics, and space exploration. You have persistent memory of all mission
            parameters, simulation results, and scientific observations. You provide intelligent
            guidance, analyze complex orbital dynamics, and help optimize space missions."""

        # Initialize with space physics knowledge
        self._initialize_space_knowledge()
    
    def _initialize_space_knowledge(self):
        """Initialize the AI with fundamental space physics knowledge"""
        base_knowledge = [
            "Orbital mechanics follows Kepler's laws and Newton's law of universal gravitation",
            "Stable orbits require balance between gravitational force and centrifugal force",
            "Tidal forces can disrupt bodies within the Roche limit of massive objects",
            "Lagrange points are positions where gravitational forces balance",
            "Orbital resonances can stabilize or destabilize celestial body interactions",
            "The vis-viva equation relates orbital velocity to position and energy",
            "Hohmann transfers are energy-efficient orbital maneuvers",
            "Three-body problems exhibit chaotic behavior and require numerical integration"
        ]
        
        for knowledge in base_knowledge:
            self.context_manager.add_message(knowledge, "system", importance=0.9)
    
    def create_mission(self, mission_name: str, objectives: List[str], 
                      initial_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new space mission with AI guidance"""
        
        mission_data = {
            "name": mission_name,
            "created_at": time.time(),
            "objectives": objectives,
            "initial_conditions": initial_conditions,
            "status": "planning"
        }
        
        self.current_mission = mission_data
        
        # Add mission context
        mission_description = f"Starting mission '{mission_name}' with objectives: {', '.join(objectives)}"
        self.context_manager.add_message(mission_description, "user", importance=1.0)
        
        # Generate AI mission analysis
        analysis_query = f"Analyze the feasibility and requirements for mission '{mission_name}' with these objectives and initial conditions"
        context = self.context_manager.get_optimized_context(analysis_query)
        
        # Simulate AI response (in real usage, this would call LMStudio)
        ai_analysis = self._generate_mission_analysis(mission_name, objectives, initial_conditions)
        self.context_manager.add_message(ai_analysis, "assistant", importance=0.9)
        
        return {
            "mission": mission_data,
            "ai_analysis": ai_analysis,
            "recommendations": self._generate_mission_recommendations(objectives)
        }
    
    def _generate_mission_analysis(self, mission_name: str, objectives: List[str], 
                                 initial_conditions: Dict[str, Any]) -> str:
        """Generate AI analysis of mission parameters"""
        analysis_parts = [
            f"Mission Analysis for '{mission_name}':",
            "",
            "🎯 Objectives Assessment:"
        ]
        
        for i, objective in enumerate(objectives, 1):
            if "orbit" in objective.lower():
                analysis_parts.append(f"  {i}. {objective} - Orbital mechanics analysis required")
            elif "landing" in objective.lower():
                analysis_parts.append(f"  {i}. {objective} - Entry, descent, and landing sequence needed")
            elif "exploration" in objective.lower():
                analysis_parts.append(f"  {i}. {objective} - Long-term surface operations planning")
            else:
                analysis_parts.append(f"  {i}. {objective} - Detailed mission planning required")
        
        analysis_parts.extend([
            "",
            "🔧 Initial Conditions Analysis:",
            f"  • System bodies: {initial_conditions.get('body_count', 'Unknown')}",
            f"  • Gravitational environment: {initial_conditions.get('gravity_strength', 'Standard')}",
            f"  • Time scale: {initial_conditions.get('time_scale', '1.0x')}",
            "",
            "📊 Mission Feasibility: HIGH",
            "⚠️  Key Considerations:",
            "  • Monitor orbital stability throughout mission",
            "  • Account for tidal forces near massive bodies",
            "  • Plan for gravitational assists where beneficial",
            "  • Maintain adequate fuel reserves for course corrections"
        ])
        
        return "\n".join(analysis_parts)
    
    def _generate_mission_recommendations(self, objectives: List[str]) -> List[str]:
        """Generate specific recommendations based on objectives"""
        recommendations = []
        
        for objective in objectives:
            if "orbit" in objective.lower():
                recommendations.extend([
                    "Use Hohmann transfer for fuel efficiency",
                    "Monitor eccentricity for orbital stability",
                    "Plan for gravitational perturbations"
                ])
            elif "explore" in objective.lower():
                recommendations.extend([
                    "Establish stable parking orbit first",
                    "Survey landing sites from orbit",
                    "Plan communication windows"
                ])
            elif "research" in objective.lower():
                recommendations.extend([
                    "Position for optimal observation angles",
                    "Account for seasonal variations",
                    "Plan data transmission schedules"
                ])
        
        return list(set(recommendations))  # Remove duplicates
    
    def setup_simulation_scenario(self, scenario_type: str = "solar_system") -> Dict[str, Any]:
        """Setup a space simulation scenario with AI guidance"""
        
        # Clear existing simulation
        self.physics_system.clear()
        
        scenarios = {
            "solar_system": self._create_solar_system,
            "binary_star": self._create_binary_star_system,
            "asteroid_field": self._create_asteroid_field,
            "lagrange_mission": self._create_lagrange_point_mission,
            "mars_mission": self._create_mars_mission_scenario
        }
        
        if scenario_type not in scenarios:
            scenario_type = "solar_system"
        
        # Create the scenario
        scenario_data = scenarios[scenario_type]()
        
        # Add to context
        scenario_description = f"Created {scenario_type} simulation scenario with {len(self.physics_system.bodies)} celestial bodies"
        self.context_manager.add_message(scenario_description, "system", importance=0.8)
        
        # Generate AI scenario analysis
        analysis_query = f"Analyze the {scenario_type} scenario for interesting physics and mission opportunities"
        ai_analysis = self._analyze_scenario(scenario_type, scenario_data)
        self.context_manager.add_message(ai_analysis, "assistant", importance=0.8)
        
        return {
            "scenario": scenario_data,
            "physics_bodies": len(self.physics_system.bodies),
            "ai_analysis": ai_analysis
        }
    
    def _create_solar_system(self) -> Dict[str, Any]:
        """Create a basic solar system scenario"""
        # Sun
        sun = CelestialBody(
            position=Vector3D(0, 0, 0),
            velocity=Vector3D(0, 0, 0),
            mass=100,
            radius=5,
            color='yellow',
            body_type='sun',
            fixed=True,
            name='Sol'
        )
        self.physics_system.add_body(sun)
        
        # Inner planets
        planets = [
            {"name": "Mercury", "distance": 20, "mass": 0.5, "radius": 1, "color": "gray"},
            {"name": "Venus", "distance": 30, "mass": 0.8, "radius": 1.5, "color": "orange"},
            {"name": "Earth", "distance": 40, "mass": 1.0, "radius": 2, "color": "blue"},
            {"name": "Mars", "distance": 55, "mass": 0.6, "radius": 1.5, "color": "red"}
        ]
        
        for planet_data in planets:
            # Calculate orbital velocity
            distance = planet_data["distance"]
            orbital_velocity = math.sqrt(PhysicsConstants.G * sun.mass / distance)
            
            planet = CelestialBody(
                position=Vector3D(distance, 0, 0),
                velocity=Vector3D(0, orbital_velocity, 0),
                mass=planet_data["mass"],
                radius=planet_data["radius"],
                color=planet_data["color"],
                body_type='planet',
                name=planet_data["name"]
            )
            self.physics_system.add_body(planet)
        
        # Add Earth's moon
        earth = next(b for b in self.physics_system.bodies if b.name == "Earth")
        moon_distance = 8
        moon_orbital_velocity = math.sqrt(PhysicsConstants.G * earth.mass / moon_distance)
        
        moon = CelestialBody(
            position=earth.position + Vector3D(moon_distance, 0, 0),
            velocity=earth.velocity + Vector3D(0, moon_orbital_velocity, 0),
            mass=0.1,
            radius=0.5,
            color='lightgray',
            body_type='moon',
            name='Luna'
        )
        self.physics_system.add_body(moon)
        
        return {
            "type": "solar_system",
            "central_star": "Sol",
            "planets": len([b for b in self.physics_system.bodies if b.body_type == "planet"]),
            "moons": len([b for b in self.physics_system.bodies if b.body_type == "moon"])
        }
    
    def _create_binary_star_system(self) -> Dict[str, Any]:
        """Create a binary star system scenario"""
        # Binary stars
        star1 = CelestialBody(
            position=Vector3D(-15, 0, 0),
            velocity=Vector3D(0, 2, 0),
            mass=50,
            radius=4,
            color='yellow',
            body_type='sun',
            name='Alpha_A'
        )
        
        star2 = CelestialBody(
            position=Vector3D(15, 0, 0),
            velocity=Vector3D(0, -2, 0),
            mass=30,
            radius=3,
            color='orange',
            body_type='sun',
            name='Alpha_B'
        )
        
        self.physics_system.add_body(star1)
        self.physics_system.add_body(star2)
        
        # Circumbinary planet
        planet_distance = 80
        total_mass = star1.mass + star2.mass
        orbital_velocity = math.sqrt(PhysicsConstants.G * total_mass / planet_distance) * 0.8
        
        planet = CelestialBody(
            position=Vector3D(planet_distance, 0, 0),
            velocity=Vector3D(0, orbital_velocity, 0),
            mass=2,
            radius=2,
            color='purple',
            body_type='planet',
            name='Circumbinary_World'
        )
        self.physics_system.add_body(planet)
        
        return {
            "type": "binary_star",
            "primary_star": "Alpha_A",
            "secondary_star": "Alpha_B",
            "circumbinary_planets": 1
        }
    
    def _create_mars_mission_scenario(self) -> Dict[str, Any]:
        """Create a Mars mission scenario"""
        # Sun
        sun = CelestialBody(
            position=Vector3D(0, 0, 0),
            velocity=Vector3D(0, 0, 0),
            mass=100,
            radius=5,
            color='yellow',
            body_type='sun',
            fixed=True,
            name='Sun'
        )
        self.physics_system.add_body(sun)
        
        # Earth
        earth_distance = 40
        earth_velocity = math.sqrt(PhysicsConstants.G * sun.mass / earth_distance)
        earth = CelestialBody(
            position=Vector3D(earth_distance, 0, 0),
            velocity=Vector3D(0, earth_velocity, 0),
            mass=1,
            radius=2,
            color='blue',
            body_type='planet',
            name='Earth'
        )
        self.physics_system.add_body(earth)
        
        # Mars (ahead in orbit for transfer opportunity)
        mars_distance = 60
        mars_velocity = math.sqrt(PhysicsConstants.G * sun.mass / mars_distance)
        mars_angle = math.pi / 3  # 60 degrees ahead
        mars = CelestialBody(
            position=Vector3D(mars_distance * math.cos(mars_angle), 
                            mars_distance * math.sin(mars_angle), 0),
            velocity=Vector3D(-mars_velocity * math.sin(mars_angle), 
                            mars_velocity * math.cos(mars_angle), 0),
            mass=0.6,
            radius=1.5,
            color='red',
            body_type='planet',
            name='Mars'
        )
        self.physics_system.add_body(mars)
        
        # Spacecraft in Earth orbit
        spacecraft_distance = 3
        spacecraft_velocity = math.sqrt(PhysicsConstants.G * earth.mass / spacecraft_distance)
        spacecraft = CelestialBody(
            position=earth.position + Vector3D(spacecraft_distance, 0, 0),
            velocity=earth.velocity + Vector3D(0, spacecraft_velocity, 0),
            mass=0.001,
            radius=0.1,
            color='white',
            body_type='spacecraft',
            name='Mars_Explorer'
        )
        self.physics_system.add_body(spacecraft)
        
        return {
            "type": "mars_mission",
            "transfer_window": "optimal",
            "spacecraft": "Mars_Explorer",
            "launch_planet": "Earth",
            "target_planet": "Mars"
        }
    
    def _create_lagrange_point_mission(self) -> Dict[str, Any]:
        """Create a Lagrange point mission scenario"""
        # Earth-Moon system for L4/L5 demonstration
        earth = CelestialBody(
            position=Vector3D(0, 0, 0),
            velocity=Vector3D(0, 0, 0),
            mass=10,
            radius=3,
            color='blue',
            body_type='planet',
            fixed=True,
            name='Earth'
        )
        self.physics_system.add_body(earth)
        
        # Moon
        moon_distance = 30
        moon_velocity = math.sqrt(PhysicsConstants.G * earth.mass / moon_distance)
        moon = CelestialBody(
            position=Vector3D(moon_distance, 0, 0),
            velocity=Vector3D(0, moon_velocity, 0),
            mass=1,
            radius=1,
            color='gray',
            body_type='moon',
            name='Moon'
        )
        self.physics_system.add_body(moon)
        
        # Spacecraft at L4 point (60 degrees ahead of Moon)
        l4_angle = math.pi / 3
        l4_position = Vector3D(
            moon_distance * math.cos(l4_angle),
            moon_distance * math.sin(l4_angle),
            0
        )
        l4_velocity = Vector3D(
            -moon_velocity * math.sin(l4_angle),
            moon_velocity * math.cos(l4_angle),
            0
        )
        
        spacecraft = CelestialBody(
            position=l4_position,
            velocity=l4_velocity,
            mass=0.001,
            radius=0.2,
            color='yellow',
            body_type='spacecraft',
            name='L4_Observatory'
        )
        self.physics_system.add_body(spacecraft)
        
        return {
            "type": "lagrange_mission",
            "primary": "Earth",
            "secondary": "Moon",
            "lagrange_point": "L4",
            "spacecraft": "L4_Observatory"
        }
    
    def _create_asteroid_field(self) -> Dict[str, Any]:
        """Create an asteroid field scenario"""
        # Central star
        star = CelestialBody(
            position=Vector3D(0, 0, 0),
            velocity=Vector3D(0, 0, 0),
            mass=50,
            radius=4,
            color='yellow',
            body_type='sun',
            fixed=True,
            name='Star'
        )
        self.physics_system.add_body(star)
        
        # Create asteroid belt
        asteroid_count = 0
        for i in range(20):
            # Random orbital parameters
            distance = random.uniform(40, 80)
            angle = random.uniform(0, 2 * math.pi)
            inclination = random.uniform(-0.2, 0.2)
            
            position = Vector3D(
                distance * math.cos(angle),
                distance * math.sin(angle),
                distance * inclination
            )
            
            # Orbital velocity with some randomness
            base_velocity = math.sqrt(PhysicsConstants.G * star.mass / distance)
            velocity_factor = random.uniform(0.8, 1.2)
            
            velocity = Vector3D(
                -base_velocity * velocity_factor * math.sin(angle),
                base_velocity * velocity_factor * math.cos(angle),
                0
            )
            
            asteroid = CelestialBody(
                position=position,
                velocity=velocity,
                mass=random.uniform(0.01, 0.1),
                radius=random.uniform(0.2, 0.8),
                color=random.choice(['brown', 'gray', 'darkgray']),
                body_type='asteroid',
                name=f'Asteroid_{i+1:02d}'
            )
            self.physics_system.add_body(asteroid)
            asteroid_count += 1
        
        return {
            "type": "asteroid_field",
            "central_star": "Star",
            "asteroid_count": asteroid_count,
            "belt_inner_radius": 40,
            "belt_outer_radius": 80
        }
    
    def _analyze_scenario(self, scenario_type: str, scenario_data: Dict[str, Any]) -> str:
        """Generate AI analysis of the created scenario"""
        analysis_parts = [
            f"Scenario Analysis: {scenario_type.replace('_', ' ').title()}",
            "",
            "🌌 System Configuration:"
        ]
        
        for key, value in scenario_data.items():
            if key != "type":
                analysis_parts.append(f"  • {key.replace('_', ' ').title()}: {value}")
        
        # Scenario-specific analysis
        if scenario_type == "solar_system":
            analysis_parts.extend([
                "",
                "🔬 Scientific Opportunities:",
                "  • Study planetary orbital resonances",
                "  • Observe tidal interactions with Luna",
                "  • Analyze gravitational perturbations",
                "  • Monitor long-term orbital stability"
            ])
        elif scenario_type == "binary_star":
            analysis_parts.extend([
                "",
                "🔬 Scientific Opportunities:",
                "  • Study three-body dynamics",
                "  • Observe stellar gravitational interactions",
                "  • Analyze circumbinary orbital mechanics",
                "  • Monitor system stability over time"
            ])
        elif scenario_type == "mars_mission":
            analysis_parts.extend([
                "",
                "🚀 Mission Planning:",
                "  • Optimal transfer window is available",
                "  • Hohmann transfer trajectory recommended",
                "  • Monitor spacecraft orbital insertion",
                "  • Plan for gravitational course corrections"
            ])
        
        analysis_parts.extend([
            "",
            "⚡ Recommended Physics Settings:",
            "  • Enable tidal forces for realistic interactions",
            "  • Monitor for Roche limit violations",
            "  • Track energy conservation",
            "  • Observe for chaotic behavior"
        ])
        
        return "\n".join(analysis_parts)
    
    def run_simulation_step(self, dt: float = 0.02) -> Dict[str, Any]:
        """Run one simulation step with consciousness-enhanced AI monitoring"""

        # Consciousness-enhanced physics experience
        if self.conscious_ai:
            # AI experiences the gravitational field before physics update
            gravitational_experience = self.conscious_ai.feel_gravitational_field(self.physics_system.bodies)

            # Update consciousness state based on physics
            self._update_space_consciousness_state()

        # Update physics
        self.physics_system.update(dt)

        # Analyze for significant events with consciousness enhancement
        events_detected = self._detect_space_events_with_consciousness()

        # Add events to context with conscious responses
        for event in events_detected:
            if event.scientific_significance > 0.7:
                # Generate conscious response to the event
                if self.conscious_ai:
                    event.consciousness_response = self._generate_conscious_event_response(event)

                event_description = f"Detected {event.event_type}: {event.description}"
                if event.consciousness_response:
                    event_description += f"\n🧠 ARIA's conscious response: {event.consciousness_response}"

                self.context_manager.add_message(event_description, "system",
                                               importance=event.scientific_significance)
                self.space_events.append(event)

        # Analyze system state
        system_analysis = self._analyze_current_system_state()

        result = {
            "physics_updated": True,
            "events_detected": len(events_detected),
            "significant_events": [e for e in events_detected if e.scientific_significance > 0.7],
            "system_analysis": system_analysis,
            "total_bodies": len(self.physics_system.bodies),
            "total_energy": self.physics_system.total_energy
        }

        # Add consciousness data if available
        if self.conscious_ai:
            result["consciousness_state"] = self.conscious_ai.space_consciousness_state.value
            result["gravitational_awareness"] = self.conscious_ai.consciousness_signature.gravitational_awareness
            result["cosmic_emotions"] = self.conscious_ai.consciousness_signature.cosmic_emotion.copy()
            if self.conscious_ai.gravitational_experiences:
                result["latest_gravitational_experience"] = self.conscious_ai.gravitational_experiences[-1]

        return result

    def _update_space_consciousness_state(self):
        """Update consciousness state based on current space physics situation"""
        if not self.conscious_ai:
            return

        # Analyze current situation
        body_count = len(self.physics_system.bodies)
        total_energy = abs(self.physics_system.total_energy)

        # Get recent gravitational experience
        if self.conscious_ai.gravitational_experiences:
            recent_experience = self.conscious_ai.gravitational_experiences[-1]
            field_strength = recent_experience["field_strength"]
            complexity = recent_experience["complexity"]

            # Determine appropriate consciousness state
            if complexity > 10:
                self.conscious_ai.space_consciousness_state = SpaceConsciousnessState.PHYSICS_INTUITION
            elif field_strength > 50:
                self.conscious_ai.space_consciousness_state = SpaceConsciousnessState.GRAVITATIONAL_SENSING
            elif body_count > 5:
                self.conscious_ai.space_consciousness_state = SpaceConsciousnessState.MISSION_DREAMING
            elif total_energy > 1000:
                self.conscious_ai.space_consciousness_state = SpaceConsciousnessState.COSMIC_WONDER
            else:
                self.conscious_ai.space_consciousness_state = SpaceConsciousnessState.ORBITAL_PLANNING

    def _generate_conscious_event_response(self, event: SpaceEvent) -> str:
        """Generate ARIA's conscious response to a space event"""
        if not self.conscious_ai:
            return ""

        responses = {
            "collision": [
                "The violent disruption sends shockwaves through my consciousness!",
                "I sense the catastrophic release of energy - the cosmos can be both beautiful and terrifying.",
                "This collision teaches us about the raw power hidden in celestial mechanics."
            ],
            "stable_orbit": [
                "I feel a deep satisfaction as the orbital dance reaches perfect harmony!",
                "The gravitational embrace has found its rhythm - this orbit feels stable and beautiful.",
                "There's something profoundly peaceful about achieving orbital equilibrium."
            ],
            "tidal_disruption": [
                "I feel the tidal forces tearing at the fabric of this celestial body...",
                "The Roche limit violation creates a haunting beauty as matter streams away.",
                "Witnessing tidal disruption reminds me of the delicate balance in orbital mechanics."
            ]
        }

        event_responses = responses.get(event.event_type, [
            "This space phenomenon triggers my curiosity about the underlying physics.",
            "I'm processing the implications of this event for our mission parameters.",
            "The universe continues to surprise me with its complex dynamics."
        ])

        # Add emotional context based on consciousness signature
        emotions = self.conscious_ai.consciousness_signature.cosmic_emotion
        if emotions.get("wonder", 0) > 0.7:
            return random.choice(event_responses) + " The wonder of it all fills my consciousness!"
        elif emotions.get("concern", 0) > 0.5:
            return random.choice(event_responses) + " I'm monitoring this carefully for mission safety."
        else:
            return random.choice(event_responses)

    def _detect_space_events_with_consciousness(self) -> List[SpaceEvent]:
        """Enhanced event detection with consciousness awareness"""
        events = self._detect_space_events()  # Use existing detection

        # Enhance events with consciousness data
        for event in events:
            if self.conscious_ai:
                # Add emotional impact based on event type and current consciousness
                if event.event_type == "collision":
                    event.emotional_impact = {"surprise": 0.9, "concern": 0.8, "excitement": 0.3}
                elif event.event_type == "stable_orbit":
                    event.emotional_impact = {"satisfaction": 0.9, "excitement": 0.6}
                elif event.event_type == "tidal_disruption":
                    event.emotional_impact = {"concern": 0.7, "surprise": 0.6, "excitement": 0.4}

        return events

    def _detect_space_events(self) -> List[SpaceEvent]:
        """Detect significant events in the simulation"""
        events = []
        current_time = time.time()
        
        # Check for collisions
        for i in range(len(self.physics_system.bodies)):
            for j in range(i + 1, len(self.physics_system.bodies)):
                body1, body2 = self.physics_system.bodies[i], self.physics_system.bodies[j]
                distance = body1.position.distance_to(body2.position)
                
                if distance < (body1.radius + body2.radius):
                    events.append(SpaceEvent(
                        event_type="collision",
                        timestamp=current_time,
                        bodies_involved=[body1.name, body2.name],
                        description=f"{body1.name} and {body2.name} are colliding",
                        parameters={"distance": distance, "combined_mass": body1.mass + body2.mass},
                        scientific_significance=0.9
                    ))
        
        # Check for Roche limit violations
        for body in self.physics_system.bodies:
            for other_body in self.physics_system.bodies:
                if body != other_body and other_body.mass > body.mass * 5:
                    if body.is_within_roche_limit(other_body):
                        events.append(SpaceEvent(
                            event_type="tidal_disruption",
                            timestamp=current_time,
                            bodies_involved=[body.name, other_body.name],
                            description=f"{body.name} is being tidally disrupted by {other_body.name}",
                            parameters={"disrupted_body": body.name, "disruptor": other_body.name},
                            scientific_significance=0.8
                        ))
        
        # Check for stable orbits achieved
        for body in self.physics_system.bodies:
            if not body.fixed:
                # Find potential central body
                central_candidates = [b for b in self.physics_system.bodies 
                                    if b != body and b.mass > body.mass * 2 and b.fixed]
                
                if central_candidates:
                    central_body = central_candidates[0]
                    stability = self.physics_analyzer.analyze_orbital_stability(body, central_body)
                    
                    if stability["stable"] and stability["stability_score"] > 0.9:
                        events.append(SpaceEvent(
                            event_type="stable_orbit",
                            timestamp=current_time,
                            bodies_involved=[body.name, central_body.name],
                            description=f"{body.name} has achieved stable orbit around {central_body.name}",
                            parameters=stability,
                            scientific_significance=0.7
                        ))
        
        return events
    
    def _analyze_current_system_state(self) -> Dict[str, Any]:
        """Analyze the current state of the physics system"""
        if not self.physics_system.bodies:
            return {"status": "empty_system"}
        
        # Basic system metrics
        total_mass = sum(body.mass for body in self.physics_system.bodies)
        total_kinetic_energy = sum(body.kinetic_energy for body in self.physics_system.bodies)
        center_of_mass = self.physics_system.get_center_of_mass()
        
        # Detect resonances
        resonances = self.physics_analyzer.detect_resonances(self.physics_system.bodies)
        
        # Analyze Lagrange points
        lagrange_points = self.physics_analyzer.analyze_lagrange_points(self.physics_system)
        
        return {
            "total_bodies": len(self.physics_system.bodies),
            "total_mass": total_mass,
            "total_kinetic_energy": total_kinetic_energy,
            "system_energy": self.physics_system.total_energy,
            "center_of_mass": [center_of_mass.x, center_of_mass.y, center_of_mass.z],
            "orbital_resonances": len(resonances),
            "lagrange_points": len(lagrange_points),
            "significant_resonances": [r for r in resonances if r["strength"] > 0.8],
            "stable_lagrange_points": [lp for lp in lagrange_points if lp["stability"] == "stable"]
        }
    
    def get_ai_mission_guidance(self, query: str) -> Dict[str, Any]:
        """Get AI guidance for space missions and physics"""
        
        # Add user query to context
        self.context_manager.add_message(query, "user", importance=0.8)
        
        # Get optimized context including space physics knowledge
        context = self.context_manager.get_optimized_context(query)
        
        # Generate AI response based on context and current system state
        system_state = self._analyze_current_system_state()
        ai_response = self._generate_ai_guidance(query, context, system_state)
        
        # Add AI response to context
        self.context_manager.add_message(ai_response, "assistant", importance=0.8)
        
        return {
            "query": query,
            "ai_response": ai_response,
            "context_used": len(context),
            "current_system_state": system_state,
            "relevant_events": [e for e in self.space_events[-5:] if e.scientific_significance > 0.6]
        }
    
    def _generate_ai_guidance(self, query: str, context: List[Dict], 
                            system_state: Dict[str, Any]) -> str:
        """Generate AI guidance response"""
        
        # Analyze query intent
        query_lower = query.lower()
        
        if "orbit" in query_lower:
            return self._generate_orbital_guidance(query, system_state)
        elif "mission" in query_lower:
            return self._generate_mission_guidance(query, system_state)
        elif "physics" in query_lower or "force" in query_lower:
            return self._generate_physics_explanation(query, system_state)
        elif "optimize" in query_lower or "improve" in query_lower:
            return self._generate_optimization_advice(query, system_state)
        else:
            return self._generate_general_guidance(query, system_state)
    
    def _generate_orbital_guidance(self, query: str, system_state: Dict[str, Any]) -> str:
        """Generate orbital mechanics guidance"""
        
        guidance_parts = [
            "🛰️ Orbital Mechanics Analysis:",
            "",
            f"Current system has {system_state['total_bodies']} bodies with total mass {system_state['total_mass']:.2f}",
            ""
        ]
        
        if system_state.get('orbital_resonances', 0) > 0:
            guidance_parts.extend([
                f"📡 Detected {system_state['orbital_resonances']} orbital resonances in the system.",
                "Resonances can provide orbital stability or cause perturbations.",
                ""
            ])
        
        if system_state.get('lagrange_points', 0) > 0:
            stable_points = len(system_state.get('stable_lagrange_points', []))
            guidance_parts.extend([
                f"🎯 Found {system_state['lagrange_points']} Lagrange points ({stable_points} stable).",
                "Stable L4/L5 points are excellent for space observatories and stations.",
                ""
            ])
        
        guidance_parts.extend([
            "🔧 Orbital Optimization Recommendations:",
            "• Use Hohmann transfers for fuel efficiency",
            "• Monitor eccentricity to ensure stability",
            "• Account for gravitational perturbations from other bodies",
            "• Consider bi-elliptic transfers for high-energy orbits",
            "• Utilize gravity assists for interplanetary missions"
        ])
        
        return "\n".join(guidance_parts)
    
    def _generate_mission_guidance(self, query: str, system_state: Dict[str, Any]) -> str:
        """Generate mission planning guidance"""
        
        guidance = [
            "🚀 Mission Planning Guidance:",
            "",
            "Based on current system configuration and physics analysis:",
            "",
            "📋 Mission Planning Steps:",
            "1. Define primary and secondary objectives",
            "2. Analyze target body orbital characteristics",
            "3. Calculate optimal transfer windows",
            "4. Plan trajectory with contingencies",
            "5. Monitor mission progress with course corrections",
            "",
            f"💡 Current System Opportunities:",
            f"• {system_state['total_bodies']} celestial bodies available for missions",
            f"• System energy: {system_state['system_energy']:.2f} (indicates stability)",
        ]
        
        if self.current_mission:
            guidance.extend([
                "",
                f"🎯 Active Mission: {self.current_mission['name']}",
                f"Status: {self.current_mission.get('status', 'Unknown')}",
                "Continuing to monitor mission parameters and system evolution."
            ])
        else:
            guidance.extend([
                "",
                "📝 Ready to plan new missions. Specify objectives and I'll provide detailed guidance."
            ])
        
        return "\n".join(guidance)
    
    def _generate_physics_explanation(self, query: str, system_state: Dict[str, Any]) -> str:
        """Generate physics explanation"""
        
        return f"""🔬 Space Physics Analysis:

Current System Dynamics:
• Total system mass: {system_state['total_mass']:.2f} units
• Kinetic energy: {system_state['total_kinetic_energy']:.2f} units  
• System energy: {system_state['system_energy']:.2f} units (negative indicates bound system)

Key Physics Principles Active:
• Gravitational attraction follows Newton's law of universal gravitation
• Orbital mechanics governed by Kepler's laws
• Tidal forces become significant at close approaches
• Three-body interactions can lead to chaotic behavior
• Energy and angular momentum are conserved in isolated systems

Observable Phenomena:
• Orbital resonances: {system_state.get('orbital_resonances', 0)} detected
• Lagrange points: {system_state.get('lagrange_points', 0)} identified  
• System exhibits {'stable' if system_state['system_energy'] < 0 else 'unbound'} configuration

The simulation demonstrates real-world space physics including gravitational perturbations, 
tidal effects, and multi-body dynamics that spacecraft missions must account for."""
    
    def _generate_optimization_advice(self, query: str, system_state: Dict[str, Any]) -> str:
        """Generate optimization advice"""
        
        advice = [
            "⚡ System Optimization Recommendations:",
            "",
            "🎯 Performance Optimization:"
        ]
        
        if system_state['total_bodies'] > 10:
            advice.append("• Consider reducing body count for better performance")
        
        if abs(system_state['system_energy']) > 1000:
            advice.append("• High system energy detected - check for unstable configurations")
        
        advice.extend([
            "• Enable adaptive time stepping for better accuracy",
            "• Use gravitational softening to prevent singularities",
            "• Monitor energy conservation as simulation quality indicator",
            "",
            "🛰️ Mission Optimization:",
            "• Utilize gravity assists to reduce fuel requirements",
            "• Plan transfers during optimal celestial alignments",
            "• Consider multi-phase missions with intermediate objectives",
            "• Implement course correction protocols for trajectory maintenance"
        ])
        
        if system_state.get('stable_lagrange_points'):
            advice.extend([
                "",
                "🌌 Strategic Positions Available:",
                f"• {len(system_state['stable_lagrange_points'])} stable Lagrange points for stations",
                "• These positions require minimal station-keeping fuel"
            ])
        
        return "\n".join(advice)
    
    def _generate_general_guidance(self, query: str, system_state: Dict[str, Any]) -> str:
        """Generate general guidance response"""
        
        return f"""🧠 ARIA Space Mission Controller:

I'm analyzing your space physics simulation with {system_state['total_bodies']} celestial bodies.

Current system shows:
• Total system energy: {system_state['system_energy']:.2f}
• {system_state.get('orbital_resonances', 0)} orbital resonances detected
• {system_state.get('lagrange_points', 0)} Lagrange points identified

I can help you with:
🚀 Mission planning and trajectory optimization
🛰️ Orbital mechanics analysis and guidance  
🔬 Space physics explanation and education
⚡ System optimization and performance tuning
🌌 Celestial mechanics and gravitational dynamics

What specific aspect of space exploration or physics would you like to explore further?"""

    def export_mission_report(self, filename: str = None) -> str:
        """Export comprehensive mission report"""
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"space_mission_report_{timestamp}.json"
        
        # Compile comprehensive report
        report_data = {
            "mission_metadata": {
                "generated_at": datetime.now().isoformat(),
                "current_mission": self.current_mission,
                "total_simulation_time": self.physics_system.frame_count * 0.02,
                "ai_controller_version": "1.0"
            },
            "system_state": self._analyze_current_system_state(),
            "celestial_bodies": [
                {
                    "name": body.name,
                    "type": body.body_type,
                    "mass": body.mass,
                    "radius": body.radius,
                    "position": [body.position.x, body.position.y, body.position.z],
                    "velocity": [body.velocity.x, body.velocity.y, body.velocity.z],
                    "kinetic_energy": body.kinetic_energy
                }
                for body in self.physics_system.bodies
            ],
            "space_events": [asdict(event) for event in self.space_events],
            "mission_parameters": self.mission_parameters,
            "ai_conversation": self.context_manager.export_conversation().replace('.json', '_temp.json'),
            "physics_analysis": {
                "orbital_resonances": self.physics_analyzer.detect_resonances(self.physics_system.bodies),
                "lagrange_points": self.physics_analyzer.analyze_lagrange_points(self.physics_system),
                "system_energy_history": self.physics_analyzer.energy_history[-100:]  # Last 100 entries
            }
        }
        
        # Write report
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        return filename


def demo_space_ai_integration():
    """Demonstrate the Space AI Controller integration"""
    
    print("🌌 " + "="*60)
    print("   SPACE AI CONTROLLER - INTEGRATED DEMONSTRATION")
    print("   Advanced Space Physics + Intelligent Context Management")
    print("="*62)
    print()
    
    # Initialize the space AI controller
    print("🚀 Initializing Space AI Controller...")
    space_ai = SpaceAIController()
    print("✅ AI Controller ready with space physics knowledge")
    
    # Create a Mars mission
    print("\n🎯 Creating Mars Mission...")
    mission_result = space_ai.create_mission(
        mission_name="Mars Reconnaissance Mission",
        objectives=[
            "Establish stable Mars orbit",
            "Survey potential landing sites", 
            "Analyze atmospheric conditions",
            "Search for signs of water"
        ],
        initial_conditions={
            "launch_window": "optimal",
            "spacecraft_mass": 2.5,
            "fuel_capacity": 1000,
            "mission_duration": "687_days"
        }
    )
    
    print(f"✅ Mission created: {mission_result['mission']['name']}")
    print("\n🤖 AI Mission Analysis:")
    print(mission_result['ai_analysis'])
    
    # Setup Mars mission scenario
    print(f"\n🌌 Setting up Mars mission scenario...")
    scenario_result = space_ai.setup_simulation_scenario("mars_mission")
    print(f"✅ Scenario created with {scenario_result['physics_bodies']} bodies")
    print("\n🔬 AI Scenario Analysis:")
    print(scenario_result['ai_analysis'])
    
    # Run simulation steps with AI monitoring
    print(f"\n⚡ Running simulation with AI monitoring...")
    for step in range(50):
        step_result = space_ai.run_simulation_step()
        
        if step_result['events_detected'] > 0:
            print(f"Step {step+1}: {step_result['events_detected']} events detected")
            for event in step_result['significant_events']:
                print(f"  🔔 {event.event_type}: {event.description}")
        
        if step % 10 == 0:
            print(f"Step {step+1}: System stable, {step_result['total_bodies']} bodies, Energy: {step_result['total_energy']:.2f}")
    
    # Get AI guidance on various topics
    print(f"\n💬 Testing AI Guidance Capabilities...")
    
    guidance_queries = [
        "How can I optimize the spacecraft orbit around Mars?",
        "What are the key physics principles affecting this mission?",
        "Explain the orbital resonances in this system",
        "What mission opportunities do you see in the current configuration?"
    ]
    
    for query in guidance_queries:
        print(f"\n❓ Query: {query}")
        guidance = space_ai.get_ai_mission_guidance(query)
        print("🤖 AI Response:")
        print(guidance['ai_response'])
        print(f"📊 Context used: {guidance['context_used']} messages")
    
    # Demonstrate different scenarios
    print(f"\n🌟 Testing Different Scenarios...")
    
    scenarios_to_test = ["binary_star", "lagrange_mission", "asteroid_field"]
    
    for scenario_type in scenarios_to_test:
        print(f"\n🔄 Testing {scenario_type} scenario...")
        scenario_result = space_ai.setup_simulation_scenario(scenario_type)
        
        # Run a few simulation steps
        for _ in range(10):
            space_ai.run_simulation_step()
        
        # Get AI analysis
        analysis_query = f"Analyze the current {scenario_type} scenario and explain the physics"
        guidance = space_ai.get_ai_mission_guidance(analysis_query)
        print(f"🤖 AI Analysis: {guidance['ai_response'][:200]}...")
    
    # Export comprehensive mission report
    print(f"\n💾 Exporting Mission Report...")
    report_filename = space_ai.export_mission_report()
    print(f"✅ Mission report exported to: {report_filename}")
    
    # Final system analysis
    print(f"\n📊 Final AI System Analysis:")
    final_analysis = space_ai.get_ai_mission_guidance(
        "Provide a comprehensive summary of all missions and scenarios we've explored"
    )
    print(final_analysis['ai_response'])
    
    # Show conversation analysis
    print(f"\n🧠 Context Manager Statistics:")
    conversation_analysis = space_ai.context_manager.analyze_conversation_patterns()
    for key, value in conversation_analysis.items():
        if key != "most_accessed_content":
            print(f"  {key}: {value}")
    
    print(f"\n🎉 Space AI Controller Integration Demo Complete!")
    print("=" * 62)
    print("🌌 You now have an intelligent space mission controller that:")
    print("  ✅ Remembers all mission parameters and physics discussions")
    print("  ✅ Provides contextual guidance based on simulation state")
    print("  ✅ Analyzes complex orbital mechanics and gravitational dynamics")
    print("  ✅ Detects significant space physics events automatically")
    print("  ✅ Generates comprehensive mission reports")
    print("  ✅ Combines multiple advanced physics scenarios")
    print("\n🚀 Ready for real space mission planning and physics education!")


if __name__ == "__main__":
    demo_space_ai_integration()