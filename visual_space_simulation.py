import math
import time
import random
import tkinter as tk
from tkinter import ttk
import threading

class Vector3D:
    """Simple 3D vector class"""
    def __init__(self, x=0.0, y=0.0, z=0.0):
        self.x = float(x)
        self.y = float(y)
        self.z = float(z)
    
    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar):
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def __truediv__(self, scalar):
        return Vector3D(self.x / scalar, self.y / scalar, self.z / scalar)
    
    def magnitude(self):
        return math.sqrt(self.x**2 + self.y**2 + self.z**2)
    
    def normalize(self):
        mag = self.magnitude()
        if mag == 0:
            return Vector3D(0, 0, 0)
        return Vector3D(self.x / mag, self.y / mag, self.z / mag)
    
    def distance_to(self, other):
        return (self - other).magnitude()
    
    def copy(self):
        return Vector3D(self.x, self.y, self.z)

class PhysicsConstants:
    G = 6.674e-11
    C = 299792458
    MIN_DISTANCE = 0.1
    TIDAL_FORCE_SCALE = 0.1
    ROCHE_LIMIT_FACTOR = 2.44

class CelestialBody:
    """Celestial body with advanced physics"""
    
    def __init__(self, position=None, velocity=None, mass=1.0, radius=1.0, 
                 color='blue', body_type='planet', fixed=False, name=None):
        self.position = position or Vector3D()
        self.velocity = velocity or Vector3D()
        self.acceleration = Vector3D()
        self.mass = mass
        self.radius = radius
        self.color = color
        self.body_type = body_type
        self.fixed = fixed
        self.name = name or f"{body_type}_{id(self)}"
        
        # Physics properties
        self.original_radius = radius
        self.kinetic_energy = 0.0
        self.trail = []
        self.max_trail_length = 100
        self.last_roche_warning = 0  # To limit warning messages
        
    def apply_force(self, force):
        """Apply a force to the body"""
        if not self.fixed:
            acceleration = force / self.mass
            self.acceleration = self.acceleration + acceleration
    
    def apply_tidal_force(self, other):
        """Apply tidal forces with limited warnings"""
        if self.fixed:
            return
            
        direction = other.position - self.position
        distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
        
        # Check Roche limit but only warn occasionally
        roche_limit = (PhysicsConstants.ROCHE_LIMIT_FACTOR * other.radius * 
                      (other.mass / self.mass)**(1/3))
        
        if distance < roche_limit:
            current_time = time.time()
            if current_time - self.last_roche_warning > 5:  # Only warn every 5 seconds
                print(f"Tidal disruption: {self.name} near {other.name}")
                self.last_roche_warning = current_time
            
            # Apply tidal effects
            tidal_strength = (PhysicsConstants.TIDAL_FORCE_SCALE * 
                             other.mass * self.mass / (distance**3))
            tidal_force = direction.normalize() * tidal_strength
            
            # Gradual mass loss
            self.mass *= 0.9999
            self.radius *= 0.9999
            
            self.apply_force(tidal_force)
    
    def apply_relativistic_corrections(self, other):
        """Apply relativistic corrections for massive objects"""
        if self.fixed or other.mass < 50:
            return
            
        direction = other.position - self.position
        distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
        speed = self.velocity.magnitude()
        
        if speed < PhysicsConstants.C * 0.1:
            beta = speed / PhysicsConstants.C
            gamma = 1 / math.sqrt(1 - beta**2) if beta < 0.9 else 10
            correction = (gamma - 1) * 0.01
            
            relativistic_force = (direction.normalize() * correction * 
                                other.mass * PhysicsConstants.G / distance**2)
            self.apply_force(relativistic_force)
    
    def update(self, dt):
        """Update position and velocity"""
        if self.fixed:
            return
            
        # Verlet integration
        new_position = (self.position + self.velocity * dt + 
                       self.acceleration * (0.5 * dt**2))
        
        self.velocity = self.velocity + self.acceleration * dt
        self.position = new_position
        self.acceleration = Vector3D()
        
        # Update trail
        if len(self.trail) == 0 or self.position.distance_to(self.trail[-1]) > 1.0:
            self.trail.append(self.position.copy())
            if len(self.trail) > self.max_trail_length:
                self.trail.pop(0)
        
        # Calculate kinetic energy
        velocity_magnitude = self.velocity.magnitude()
        self.kinetic_energy = 0.5 * self.mass * velocity_magnitude**2

class AdvancedPhysicsSystem:
    """N-body physics system with advanced gravitational dynamics"""
    
    def __init__(self):
        self.bodies = []
        self.gravitational_constant = 1.0
        self.time_scale = 1.0
        self.enable_tidal_forces = True
        self.enable_relativistic_effects = False
        self.enable_gravitational_waves = False
        self.total_energy = 0.0
        self.frame_count = 0
        
    def add_body(self, body):
        """Add a celestial body to the system"""
        self.bodies.append(body)
    
    def clear(self):
        """Clear all bodies from the system"""
        self.bodies.clear()
    
    def calculate_gravitational_forces(self):
        """Calculate gravitational forces between all bodies"""
        # Reset accelerations
        for body in self.bodies:
            body.acceleration = Vector3D()
        
        # Calculate pairwise forces
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                
                direction = body2.position - body1.position
                distance = max(direction.magnitude(), PhysicsConstants.MIN_DISTANCE)
                
                # Basic gravitational force with softening
                softening_length = 0.1
                softened_distance = math.sqrt(distance**2 + softening_length**2)
                force_magnitude = (PhysicsConstants.G * self.gravitational_constant * 
                                 body1.mass * body2.mass / softened_distance**2)
                
                force_vector = direction.normalize() * force_magnitude
                
                # Apply Newton's third law
                body1.apply_force(force_vector)
                body2.apply_force(force_vector * -1)
                
                # Apply advanced physics effects
                if self.enable_tidal_forces:
                    body1.apply_tidal_force(body2)
                    body2.apply_tidal_force(body1)
                
                if self.enable_relativistic_effects:
                    body1.apply_relativistic_corrections(body2)
                    body2.apply_relativistic_corrections(body1)
    
    def update(self, dt):
        """Update the physics system"""
        scaled_dt = dt * self.time_scale
        self.calculate_gravitational_forces()
        
        for body in self.bodies:
            body.update(scaled_dt)
        
        self.frame_count += 1
    
    def get_center_of_mass(self):
        """Calculate center of mass"""
        total_mass = sum(body.mass for body in self.bodies)
        if total_mass == 0:
            return Vector3D()
            
        com_position = Vector3D()
        for body in self.bodies:
            weighted_position = body.position * body.mass
            com_position = com_position + weighted_position
        
        return com_position / total_mass

class VisualSpaceSimulation:
    """Visual space simulation with tkinter graphics"""
    
    def __init__(self):
        self.physics_system = AdvancedPhysicsSystem()
        self.is_running = False
        self.show_trails = True
        self.show_velocity_vectors = False
        
        # Setup GUI
        self.setup_gui()
        self.create_default_system()
        
        # Start simulation
        self.start_simulation()
    
    def setup_gui(self):
        """Setup the main GUI window"""
        self.root = tk.Tk()
        self.root.title("Visual Space Physics Simulation")
        self.root.geometry("1200x800")
        self.root.configure(bg='black')
        
        # Main container
        main_frame = tk.Frame(self.root, bg='black')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Canvas for simulation
        self.canvas = tk.Canvas(main_frame, width=800, height=600, bg='black', highlightthickness=0)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Control panel
        control_frame = tk.Frame(main_frame, width=300, bg='#1a1a1a')
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        control_frame.pack_propagate(False)
        
        self.setup_controls(control_frame)
        
        # Create starfield
        self.create_starfield()
    
    def setup_controls(self, parent):
        """Setup control panel"""
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Dark.TLabel', background='#1a1a1a', foreground='white')
        style.configure('Dark.TFrame', background='#1a1a1a')
        style.configure('Dark.TButton', background='#3a3a3a', foreground='white')
        
        # Title
        title_label = tk.Label(parent, text="Space Physics Simulation", 
                               font=('Arial', 14, 'bold'), fg='cyan', bg='#1a1a1a')
        title_label.pack(pady=(0, 20))
        
        # Simulation Controls
        sim_frame = tk.LabelFrame(parent, text="Simulation Controls", 
                                 fg='lightgreen', bg='#1a1a1a', font=('Arial', 10, 'bold'))
        sim_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        # Time Scale
        tk.Label(sim_frame, text="Time Scale:", fg='white', bg='#1a1a1a').pack(anchor=tk.W)
        self.time_scale_var = tk.DoubleVar(value=1.0)
        time_scale_scale = tk.Scale(sim_frame, from_=0.1, to=5.0, orient=tk.HORIZONTAL, 
                                   variable=self.time_scale_var, resolution=0.1,
                                   command=self.update_time_scale, bg='#2a2a2a', fg='white',
                                   highlightbackground='#1a1a1a', troughcolor='#3a3a3a')
        time_scale_scale.pack(fill=tk.X)
        
        # Gravitational Constant
        tk.Label(sim_frame, text="Gravity Strength:", fg='white', bg='#1a1a1a').pack(anchor=tk.W, pady=(10, 0))
        self.grav_const_var = tk.DoubleVar(value=1.0)
        grav_const_scale = tk.Scale(sim_frame, from_=0.1, to=3.0, orient=tk.HORIZONTAL,
                                   variable=self.grav_const_var, resolution=0.1,
                                   command=self.update_grav_constant, bg='#2a2a2a', fg='white',
                                   highlightbackground='#1a1a1a', troughcolor='#3a3a3a')
        grav_const_scale.pack(fill=tk.X)
        
        # Control buttons
        button_frame = tk.Frame(sim_frame, bg='#1a1a1a')
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.pause_button = tk.Button(button_frame, text="Pause", command=self.toggle_pause,
                                     bg='#4a4a4a', fg='white', font=('Arial', 9))
        self.pause_button.pack(side=tk.LEFT, padx=(0, 5))
        
        reset_button = tk.Button(button_frame, text="Reset", command=self.reset_simulation,
                               bg='#4a4a4a', fg='white', font=('Arial', 9))
        reset_button.pack(side=tk.LEFT)
        
        # Visualization Controls
        viz_frame = tk.LabelFrame(parent, text="Visualization", 
                                 fg='lightgreen', bg='#1a1a1a', font=('Arial', 10, 'bold'))
        viz_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        self.show_trails_var = tk.BooleanVar(value=True)
        trails_check = tk.Checkbutton(viz_frame, text="Show Orbital Trails", 
                                     variable=self.show_trails_var, command=self.toggle_trails,
                                     fg='white', bg='#1a1a1a', selectcolor='#3a3a3a')
        trails_check.pack(anchor=tk.W)
        
        self.show_velocity_var = tk.BooleanVar(value=False)
        velocity_check = tk.Checkbutton(viz_frame, text="Show Velocity Vectors",
                                       variable=self.show_velocity_var, command=self.toggle_velocity,
                                       fg='white', bg='#1a1a1a', selectcolor='#3a3a3a')
        velocity_check.pack(anchor=tk.W)
        
        # Physics Controls
        physics_frame = tk.LabelFrame(parent, text="Advanced Physics", 
                                     fg='lightgreen', bg='#1a1a1a', font=('Arial', 10, 'bold'))
        physics_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        self.tidal_forces_var = tk.BooleanVar(value=True)
        tidal_check = tk.Checkbutton(physics_frame, text="Tidal Forces",
                                    variable=self.tidal_forces_var, command=self.toggle_tidal,
                                    fg='white', bg='#1a1a1a', selectcolor='#3a3a3a')
        tidal_check.pack(anchor=tk.W)
        
        self.relativistic_var = tk.BooleanVar(value=False)
        rel_check = tk.Checkbutton(physics_frame, text="Relativistic Effects",
                                  variable=self.relativistic_var, command=self.toggle_relativistic,
                                  fg='white', bg='#1a1a1a', selectcolor='#3a3a3a')
        rel_check.pack(anchor=tk.W)
        
        # Add Bodies
        add_frame = tk.LabelFrame(parent, text="Add Celestial Bodies", 
                                 fg='lightgreen', bg='#1a1a1a', font=('Arial', 10, 'bold'))
        add_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        # Body parameters
        tk.Label(add_frame, text="Mass:", fg='white', bg='#1a1a1a').pack(anchor=tk.W)
        self.body_mass_var = tk.DoubleVar(value=1.0)
        mass_scale = tk.Scale(add_frame, from_=0.1, to=10.0, orient=tk.HORIZONTAL,
                             variable=self.body_mass_var, resolution=0.1,
                             bg='#2a2a2a', fg='white', highlightbackground='#1a1a1a', troughcolor='#3a3a3a')
        mass_scale.pack(fill=tk.X)
        
        # Add buttons
        sun_button = tk.Button(add_frame, text="Add Sun", command=self.add_sun,
                              bg='orange', fg='black', font=('Arial', 9, 'bold'))
        sun_button.pack(fill=tk.X, pady=(5, 2))
        
        planet_button = tk.Button(add_frame, text="Add Planet", command=self.add_planet,
                                 bg='lightblue', fg='black', font=('Arial', 9, 'bold'))
        planet_button.pack(fill=tk.X, pady=2)
        
        # Information Panel
        info_frame = tk.LabelFrame(parent, text="System Info", 
                                  fg='lightgreen', bg='#1a1a1a', font=('Arial', 10, 'bold'))
        info_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        self.body_count_label = tk.Label(info_frame, text="Bodies: 0", 
                                        fg='cyan', bg='#1a1a1a', font=('Arial', 9))
        self.body_count_label.pack(anchor=tk.W)
        
        self.fps_label = tk.Label(info_frame, text="FPS: 0", 
                                 fg='cyan', bg='#1a1a1a', font=('Arial', 9))
        self.fps_label.pack(anchor=tk.W)
        
        # Instructions
        instr_frame = tk.LabelFrame(parent, text="Instructions", 
                                   fg='lightgreen', bg='#1a1a1a', font=('Arial', 10, 'bold'))
        instr_frame.pack(fill=tk.X, padx=5)
        
        instructions = tk.Text(instr_frame, height=6, width=30, bg='#2a2a2a', fg='white',
                              font=('Arial', 8), wrap=tk.WORD)
        instructions.pack(fill=tk.BOTH, expand=True)
        instructions.insert(tk.END, 
            "• Click canvas to add planets\n"
            "• Shift+Click to add suns\n"
            "• Watch orbital mechanics!\n"
            "• Enable tidal forces to see\n"
            "  bodies get disrupted\n"
            "• Adjust time scale for\n"
            "  faster/slower motion")
        instructions.config(state=tk.DISABLED)
    
    def create_starfield(self):
        """Create background stars"""
        for _ in range(200):
            x = random.randint(0, 800)
            y = random.randint(0, 600)
            size = random.choice([1, 1, 1, 2])
            self.canvas.create_oval(x, y, x+size, y+size, fill='white', outline='white')
    
    def create_default_system(self):
        """Create a default solar system"""
        # Create Sun
        sun = CelestialBody(
            position=Vector3D(400, 300, 0),  # Center of canvas
            velocity=Vector3D(0, 0, 0),
            mass=20,
            radius=15,
            color='yellow',
            body_type='sun',
            fixed=True,
            name='Sun'
        )
        self.physics_system.add_body(sun)
        
        # Create Earth
        earth = CelestialBody(
            position=Vector3D(550, 300, 0),
            velocity=Vector3D(0, 3, 0),
            mass=1,
            radius=5,
            color='blue',
            body_type='planet',
            name='Earth'
        )
        self.physics_system.add_body(earth)
        
        # Create Moon
        moon = CelestialBody(
            position=Vector3D(570, 300, 0),
            velocity=Vector3D(0, 4, 0),
            mass=0.1,
            radius=2,
            color='gray',
            body_type='moon',
            name='Moon'
        )
        self.physics_system.add_body(moon)
    
    def start_simulation(self):
        """Start the simulation loop"""
        self.is_running = True
        self.last_time = time.time()
        self.frame_count = 0
        self.fps_time = time.time()
        
        # Bind mouse events
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<Shift-Button-1>", self.on_canvas_shift_click)
        self.canvas.focus_set()
        
        self.animate()
    
    def animate(self):
        """Animation loop"""
        if self.is_running:
            current_time = time.time()
            dt = min(current_time - self.last_time, 0.05)  # Cap dt
            self.last_time = current_time
            
            # Update physics
            self.physics_system.update(dt)
            
            # Clear canvas (except stars)
            self.canvas.delete("body", "trail", "vector")
            
            # Draw all bodies and their trails
            for body in self.physics_system.bodies:
                self.draw_body(body)
            
            # Update info
            self.update_info_panel()
            
            # Calculate FPS
            self.frame_count += 1
            if time.time() - self.fps_time > 1.0:
                fps = self.frame_count / (time.time() - self.fps_time)
                self.fps_label.config(text=f"FPS: {fps:.1f}")
                self.frame_count = 0
                self.fps_time = time.time()
        
        # Schedule next frame
        self.root.after(16, self.animate)  # ~60 FPS
    
    def draw_body(self, body):
        """Draw a celestial body and its trail"""
        x, y = body.position.x, body.position.y
        r = max(body.radius, 2)  # Minimum visible size
        
        # Choose color based on body type
        color_map = {
            'sun': 'yellow',
            'planet': body.color,
            'moon': 'lightgray',
            'asteroid': 'brown'
        }
        color = color_map.get(body.body_type, body.color)
        
        # Draw trail
        if self.show_trails and len(body.trail) > 1:
            trail_points = []
            for point in body.trail:
                trail_points.extend([point.x, point.y])
            
            if len(trail_points) >= 4:
                self.canvas.create_line(trail_points, fill=color, width=1, 
                                       smooth=True, tags="trail")
        
        # Draw body
        if body.body_type == 'sun':
            # Glowing effect for suns
            for i in range(3):
                radius = r + i * 2
                alpha_color = color if i == 0 else '#FFFF99'
                self.canvas.create_oval(x - radius, y - radius, x + radius, y + radius,
                                       fill=alpha_color, outline=alpha_color, tags="body")
        else:
            self.canvas.create_oval(x - r, y - r, x + r, y + r,
                                   fill=color, outline='white', width=1, tags="body")
        
        # Draw name
        self.canvas.create_text(x, y - r - 10, text=body.name, fill='white', 
                               font=('Arial', 8), tags="body")
        
        # Draw velocity vector
        if self.show_velocity_vectors:
            vel_scale = 20
            end_x = x + body.velocity.x * vel_scale
            end_y = y + body.velocity.y * vel_scale
            
            if abs(end_x - x) > 1 or abs(end_y - y) > 1:
                self.canvas.create_line(x, y, end_x, end_y, fill='red', width=2,
                                       arrow=tk.LAST, arrowshape=(10, 12, 3), tags="vector")
    
    def update_info_panel(self):
        """Update information panel"""
        self.body_count_label.config(text=f"Bodies: {len(self.physics_system.bodies)}")
    
    # Event handlers
    def on_canvas_click(self, event):
        """Add planet at click position"""
        self.add_planet_at_position(event.x, event.y)
    
    def on_canvas_shift_click(self, event):
        """Add sun at shift+click position"""
        self.add_sun_at_position(event.x, event.y)
    
    def add_planet_at_position(self, x, y):
        """Add a planet at specified position"""
        # Find nearest massive body for orbital velocity
        central_body = None
        min_distance = float('inf')
        
        for body in self.physics_system.bodies:
            if body.mass > 5:  # Massive enough to orbit
                distance = math.sqrt((x - body.position.x)**2 + (y - body.position.y)**2)
                if distance < min_distance:
                    min_distance = distance
                    central_body = body
        
        # Calculate orbital velocity
        if central_body and min_distance > 30:
            angle = math.atan2(y - central_body.position.y, x - central_body.position.x)
            mu = PhysicsConstants.G * self.physics_system.gravitational_constant * central_body.mass
            orbital_speed = math.sqrt(mu / min_distance) * 0.5  # Reduced for stability
            
            vel_x = -math.sin(angle) * orbital_speed
            vel_y = math.cos(angle) * orbital_speed
        else:
            vel_x = random.uniform(-1, 1)
            vel_y = random.uniform(-1, 1)
        
        planet = CelestialBody(
            position=Vector3D(x, y, 0),
            velocity=Vector3D(vel_x, vel_y, 0),
            mass=self.body_mass_var.get(),
            radius=3 + self.body_mass_var.get(),
            color=random.choice(['blue', 'red', 'green', 'purple', 'cyan']),
            body_type='planet',
            name=f'Planet_{len([b for b in self.physics_system.bodies if b.body_type == "planet"]) + 1}'
        )
        
        self.physics_system.add_body(planet)
    
    def add_sun_at_position(self, x, y):
        """Add a sun at specified position"""
        sun = CelestialBody(
            position=Vector3D(x, y, 0),
            velocity=Vector3D(random.uniform(-0.5, 0.5), random.uniform(-0.5, 0.5), 0),
            mass=self.body_mass_var.get() * 10,
            radius=8 + self.body_mass_var.get() * 2,
            color='orange',
            body_type='sun',
            name=f'Sun_{len([b for b in self.physics_system.bodies if b.body_type == "sun"]) + 1}'
        )
        
        self.physics_system.add_body(sun)
    
    # Control callbacks
    def update_time_scale(self, value):
        self.physics_system.time_scale = float(value)
    
    def update_grav_constant(self, value):
        self.physics_system.gravitational_constant = float(value)
    
    def toggle_pause(self):
        self.is_running = not self.is_running
        self.pause_button.config(text="Play" if not self.is_running else "Pause")
    
    def reset_simulation(self):
        self.physics_system.clear()
        self.canvas.delete("body", "trail", "vector")
        self.create_default_system()
    
    def toggle_trails(self):
        self.show_trails = self.show_trails_var.get()
    
    def toggle_velocity(self):
        self.show_velocity_vectors = self.show_velocity_var.get()
    
    def toggle_tidal(self):
        self.physics_system.enable_tidal_forces = self.tidal_forces_var.get()
    
    def toggle_relativistic(self):
        self.physics_system.enable_relativistic_effects = self.relativistic_var.get()
    
    def add_sun(self):
        """Add a new sun randomly"""
        x = random.randint(100, 700)
        y = random.randint(100, 500)
        self.add_sun_at_position(x, y)
    
    def add_planet(self):
        """Add a new planet randomly"""
        x = random.randint(100, 700)
        y = random.randint(100, 500)
        self.add_planet_at_position(x, y)
    
    def run(self):
        """Start the GUI main loop"""
        print("🚀 Visual Space Physics Simulation Started!")
        print("✨ Features:")
        print("   • Real-time gravitational physics")
        print("   • Tidal forces and disruption")
        print("   • Orbital mechanics visualization")
        print("   • Interactive celestial body creation")
        print("   • Advanced gravitational dynamics")
        print("\n🎮 Controls:")
        print("   • Click canvas to add planets")
        print("   • Shift+Click to add suns")
        print("   • Use sliders to control physics")
        print("   • Watch the cosmic dance unfold!")
        self.root.mainloop()

if __name__ == "__main__":
    app = VisualSpaceSimulation()
    app.run()