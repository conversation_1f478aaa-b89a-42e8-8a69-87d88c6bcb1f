import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from mpl_toolkits.mplot3d import Axes3D
import tkinter as tk
from tkinter import ttk
import threading
import time
from space_simulation import *

class SpaceSimulationGUI:
    """Interactive GUI for the space physics simulation"""
    
    def __init__(self):
        self.physics_system = PhysicsSystem()
        self.gravity_field = None
        self.lagrange_points = []
        
        # Animation control
        self.animation = None
        self.is_running = False
        self.dt = 0.01
        
        # Visualization settings
        self.show_orbits = True
        self.show_velocity_vectors = False
        self.show_grid = True
        self.show_gravity_field = False
        self.show_lagrange_points = False
        
        # Setup GUI
        self.setup_gui()
        self.setup_matplotlib()
        self.create_default_system()
        
        # Start animation
        self.start_animation()
    
    def setup_gui(self):
        """Setup the main GUI window and controls"""
        self.root = tk.Tk()
        self.root.title("Advanced Space Physics Simulation")
        self.root.geometry("1400x800")
        
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for matplotlib
        self.plot_frame = ttk.Frame(main_frame)
        self.plot_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Right panel for controls
        control_frame = ttk.Frame(main_frame, width=300)
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        control_frame.pack_propagate(False)
        
        self.setup_controls(control_frame)
    
    def setup_controls(self, parent):
        """Setup control panel"""
        # Title
        title_label = ttk.Label(parent, text="Space Physics Simulation", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Simulation Controls
        sim_frame = ttk.LabelFrame(parent, text="Simulation Controls", padding=10)
        sim_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Time Scale
        ttk.Label(sim_frame, text="Time Scale:").pack(anchor=tk.W)
        self.time_scale_var = tk.DoubleVar(value=1.0)
        time_scale_scale = ttk.Scale(sim_frame, from_=0.1, to=5.0, 
                                   orient=tk.HORIZONTAL, variable=self.time_scale_var,
                                   command=self.update_time_scale)
        time_scale_scale.pack(fill=tk.X)
        self.time_scale_label = ttk.Label(sim_frame, text="1.0x")
        self.time_scale_label.pack(anchor=tk.W)
        
        # Gravitational Constant
        ttk.Label(sim_frame, text="Gravitational Constant:").pack(anchor=tk.W, pady=(10, 0))
        self.grav_const_var = tk.DoubleVar(value=1.0)
        grav_const_scale = ttk.Scale(sim_frame, from_=0.1, to=3.0,
                                   orient=tk.HORIZONTAL, variable=self.grav_const_var,
                                   command=self.update_grav_constant)
        grav_const_scale.pack(fill=tk.X)
        self.grav_const_label = ttk.Label(sim_frame, text="1.0")
        self.grav_const_label.pack(anchor=tk.W)
        
        # Control buttons
        button_frame = ttk.Frame(sim_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.pause_button = ttk.Button(button_frame, text="Pause", command=self.toggle_pause)
        self.pause_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(button_frame, text="Reset", command=self.reset_simulation).pack(side=tk.LEFT)
        
        # Visualization Controls
        viz_frame = ttk.LabelFrame(parent, text="Visualization", padding=10)
        viz_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.show_orbits_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(viz_frame, text="Show Orbital Paths", 
                       variable=self.show_orbits_var,
                       command=self.toggle_orbits).pack(anchor=tk.W)
        
        self.show_velocity_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(viz_frame, text="Show Velocity Vectors",
                       variable=self.show_velocity_var,
                       command=self.toggle_velocity_vectors).pack(anchor=tk.W)
        
        self.show_grid_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(viz_frame, text="Show Grid",
                       variable=self.show_grid_var,
                       command=self.toggle_grid).pack(anchor=tk.W)
        
        self.show_gravity_field_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(viz_frame, text="Show Gravity Field",
                       variable=self.show_gravity_field_var,
                       command=self.toggle_gravity_field).pack(anchor=tk.W)
        
        self.show_lagrange_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(viz_frame, text="Show Lagrange Points",
                       variable=self.show_lagrange_var,
                       command=self.toggle_lagrange_points).pack(anchor=tk.W)
        
        # Advanced Physics Controls
        physics_frame = ttk.LabelFrame(parent, text="Advanced Physics", padding=10)
        physics_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.tidal_forces_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(physics_frame, text="Enable Tidal Forces",
                       variable=self.tidal_forces_var,
                       command=self.toggle_tidal_forces).pack(anchor=tk.W)
        
        self.relativistic_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(physics_frame, text="Enable Relativistic Effects",
                       variable=self.relativistic_var,
                       command=self.toggle_relativistic).pack(anchor=tk.W)
        
        self.grav_waves_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(physics_frame, text="Enable Gravitational Waves",
                       variable=self.grav_waves_var,
                       command=self.toggle_grav_waves).pack(anchor=tk.W)
        
        # Tidal Force Strength
        ttk.Label(physics_frame, text="Tidal Force Strength:").pack(anchor=tk.W, pady=(10, 0))
        self.tidal_strength_var = tk.DoubleVar(value=1.0)
        tidal_strength_scale = ttk.Scale(physics_frame, from_=0.0, to=3.0,
                                       orient=tk.HORIZONTAL, variable=self.tidal_strength_var,
                                       command=self.update_tidal_strength)
        tidal_strength_scale.pack(fill=tk.X)
        self.tidal_strength_label = ttk.Label(physics_frame, text="1.0")
        self.tidal_strength_label.pack(anchor=tk.W)
        
        # Add Bodies
        add_frame = ttk.LabelFrame(parent, text="Add Celestial Bodies", padding=10)
        add_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Body parameters
        ttk.Label(add_frame, text="Mass:").pack(anchor=tk.W)
        self.body_mass_var = tk.DoubleVar(value=1.0)
        body_mass_scale = ttk.Scale(add_frame, from_=0.1, to=20.0,
                                  orient=tk.HORIZONTAL, variable=self.body_mass_var)
        body_mass_scale.pack(fill=tk.X)
        
        ttk.Label(add_frame, text="Radius:").pack(anchor=tk.W, pady=(5, 0))
        self.body_radius_var = tk.DoubleVar(value=1.0)
        body_radius_scale = ttk.Scale(add_frame, from_=0.5, to=5.0,
                                    orient=tk.HORIZONTAL, variable=self.body_radius_var)
        body_radius_scale.pack(fill=tk.X)
        
        ttk.Label(add_frame, text="Initial Velocity:").pack(anchor=tk.W, pady=(5, 0))
        self.initial_velocity_var = tk.DoubleVar(value=2.0)
        initial_velocity_scale = ttk.Scale(add_frame, from_=0.0, to=10.0,
                                         orient=tk.HORIZONTAL, variable=self.initial_velocity_var)
        initial_velocity_scale.pack(fill=tk.X)
        
        # Add buttons
        add_button_frame = ttk.Frame(add_frame)
        add_button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(add_button_frame, text="Add Sun", command=self.add_sun).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(add_button_frame, text="Add Planet", command=self.add_planet).pack(side=tk.LEFT)
        
        # Information Panel
        info_frame = ttk.LabelFrame(parent, text="System Information", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.body_count_label = ttk.Label(info_frame, text="Bodies: 0")
        self.body_count_label.pack(anchor=tk.W)
        
        self.total_energy_label = ttk.Label(info_frame, text="Total Energy: 0.0")
        self.total_energy_label.pack(anchor=tk.W)
        
        self.fps_label = ttk.Label(info_frame, text="FPS: 0")
        self.fps_label.pack(anchor=tk.W)
    
    def setup_matplotlib(self):
        """Setup matplotlib 3D plot"""
        plt.style.use('dark_background')
        
        self.fig = plt.figure(figsize=(10, 8), facecolor='black')
        self.ax = self.fig.add_subplot(111, projection='3d', facecolor='black')
        
        # Setup plot
        self.ax.set_xlim([-30, 30])
        self.ax.set_ylim([-30, 30])
        self.ax.set_zlim([-30, 30])
        self.ax.set_xlabel('X', color='white')
        self.ax.set_ylabel('Y', color='white')
        self.ax.set_zlabel('Z', color='white')
        self.ax.tick_params(colors='white')
        
        # Create starfield background
        self.create_starfield()
        
        # Embed in tkinter
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        self.canvas = FigureCanvasTkAgg(self.fig, self.plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def create_starfield(self):
        """Create background starfield"""
        n_stars = 500
        star_positions = np.random.uniform(-50, 50, (n_stars, 3))
        self.ax.scatter(star_positions[:, 0], star_positions[:, 1], star_positions[:, 2],
                       c='white', s=0.5, alpha=0.6)
    
    def create_default_system(self):
        """Create default sun-planet system"""
        # Create sun
        sun = CelestialBody(
            position=Vector3D(0, 0, 0),
            velocity=Vector3D(0, 0, 0),
            mass=10,
            radius=3,
            color='yellow',
            body_type='sun',
            fixed=True,
            name='Sun'
        )
        
        # Create planet
        planet = CelestialBody(
            position=Vector3D(15, 0, 0),
            velocity=Vector3D(0, 0, 2.5),
            mass=1,
            radius=1,
            color='blue',
            body_type='planet',
            name='Planet'
        )
        
        self.physics_system.add_body(sun)
        self.physics_system.add_body(planet)
        
        self.update_info_panel()
    
    def start_animation(self):
        """Start the animation loop"""
        self.is_running = True
        self.animation = FuncAnimation(self.fig, self.animate, interval=16, blit=False)
        self.last_time = time.time()
        self.frame_count = 0
    
    def animate(self, frame):
        """Animation function called by matplotlib"""
        if not self.is_running:
            return
        
        # Update physics
        current_time = time.time()
        dt = min(current_time - self.last_time, 0.1)  # Cap dt to prevent instability
        self.last_time = current_time
        
        self.physics_system.update(dt)
        
        # Clear and redraw
        self.ax.clear()
        self.create_starfield()
        
        # Set plot properties
        self.ax.set_xlim([-30, 30])
        self.ax.set_ylim([-30, 30])
        self.ax.set_zlim([-30, 30])
        self.ax.set_xlabel('X', color='white')
        self.ax.set_ylabel('Y', color='white')
        self.ax.set_zlabel('Z', color='white')
        self.ax.tick_params(colors='white')
        
        # Draw grid
        if self.show_grid:
            self.ax.grid(True, alpha=0.3)
        
        # Draw gravity field
        if self.show_gravity_field:
            self.draw_gravity_field()
        
        # Draw Lagrange points
        if self.show_lagrange_points:
            self.draw_lagrange_points()
        
        # Draw bodies and trails
        for body in self.physics_system.bodies:
            self.draw_body(body)
        
        # Update info panel
        self.update_info_panel()
        
        # Calculate FPS
        self.frame_count += 1
        if self.frame_count % 30 == 0:  # Update FPS every 30 frames
            fps = 30 / (time.time() - getattr(self, 'fps_time', time.time()))
            self.fps_label.config(text=f"FPS: {fps:.1f}")
            self.fps_time = time.time()
    
    def draw_body(self, body):
        """Draw a celestial body"""
        pos = body.position.data
        
        # Choose color based on body type
        color_map = {
            'sun': 'yellow',
            'planet': 'blue',
            'asteroid': 'gray',
            'moon': 'lightgray'
        }
        color = color_map.get(body.body_type, body.color)
        
        # Draw body
        size = max(body.radius * 20, 10)
        self.ax.scatter(pos[0], pos[1], pos[2], c=color, s=size, alpha=0.8)
        
        # Draw name
        self.ax.text(pos[0], pos[1], pos[2] + body.radius + 1, body.name, 
                    color='white', fontsize=8)
        
        # Draw trail
        if self.show_orbits and len(body.trail) > 1:
            trail_points = np.array([point.data for point in body.trail])
            self.ax.plot(trail_points[:, 0], trail_points[:, 1], trail_points[:, 2],
                        color=color, alpha=0.6, linewidth=1)
        
        # Draw velocity vector
        if self.show_velocity_vectors:
            vel = body.velocity.data
            speed = np.linalg.norm(vel)
            if speed > 0.1:
                vel_normalized = vel / speed
                arrow_length = min(speed * 2, 5)
                end_pos = pos + vel_normalized * arrow_length
                self.ax.plot([pos[0], end_pos[0]], [pos[1], end_pos[1]], [pos[2], end_pos[2]],
                           color='red', linewidth=2, alpha=0.8)
    
    def draw_gravity_field(self):
        """Draw gravitational field visualization"""
        if not self.gravity_field:
            self.gravity_field = GravityField(self.physics_system.bodies)
        else:
            self.gravity_field.bodies = self.physics_system.bodies
            self.gravity_field.update_field()
        
        if self.gravity_field.field_points:
            points = np.array(self.gravity_field.field_points)
            strengths = np.array(self.gravity_field.field_strengths)
            
            # Normalize strengths for color mapping
            normalized_strengths = strengths / np.max(strengths) if np.max(strengths) > 0 else strengths
            
            self.ax.scatter(points[:, 0], points[:, 1], points[:, 2],
                           c=normalized_strengths, cmap='plasma', s=2, alpha=0.6)
    
    def draw_lagrange_points(self):
        """Draw Lagrange points"""
        # Find two most massive bodies
        sorted_bodies = sorted(self.physics_system.bodies, key=lambda b: b.mass, reverse=True)
        if len(sorted_bodies) >= 2:
            lagrange_points = self.physics_system.find_lagrange_points(sorted_bodies[0], sorted_bodies[1])
            
            for point in lagrange_points:
                pos = point['position'].data
                self.ax.scatter(pos[0], pos[1], pos[2], c='lime', s=50, marker='o', alpha=0.8)
                self.ax.text(pos[0], pos[1], pos[2] + 1, point['type'], 
                           color='lime', fontsize=8)
    
    def update_info_panel(self):
        """Update information panel"""
        self.body_count_label.config(text=f"Bodies: {len(self.physics_system.bodies)}")
        self.total_energy_label.config(text=f"Total Energy: {self.physics_system.total_energy:.2e}")
    
    # Control callbacks
    def update_time_scale(self, value):
        self.physics_system.time_scale = float(value)
        self.time_scale_label.config(text=f"{float(value):.1f}x")
    
    def update_grav_constant(self, value):
        self.physics_system.gravitational_constant = float(value)
        self.grav_const_label.config(text=f"{float(value):.1f}")
    
    def update_tidal_strength(self, value):
        self.physics_system.tidal_force_strength = float(value)
        self.tidal_strength_label.config(text=f"{float(value):.1f}")
    
    def toggle_pause(self):
        self.is_running = not self.is_running
        self.pause_button.config(text="Play" if not self.is_running else "Pause")
    
    def reset_simulation(self):
        self.physics_system.clear()
        self.create_default_system()
    
    def toggle_orbits(self):
        self.show_orbits = self.show_orbits_var.get()
    
    def toggle_velocity_vectors(self):
        self.show_velocity_vectors = self.show_velocity_var.get()
    
    def toggle_grid(self):
        self.show_grid = self.show_grid_var.get()
    
    def toggle_gravity_field(self):
        self.show_gravity_field = self.show_gravity_field_var.get()
    
    def toggle_lagrange_points(self):
        self.show_lagrange_points = self.show_lagrange_var.get()
    
    def toggle_tidal_forces(self):
        self.physics_system.enable_tidal_forces = self.tidal_forces_var.get()
    
    def toggle_relativistic(self):
        self.physics_system.enable_relativistic_effects = self.relativistic_var.get()
    
    def toggle_grav_waves(self):
        self.physics_system.enable_gravitational_waves = self.grav_waves_var.get()
    
    def add_sun(self):
        """Add a new sun to the system"""
        angle = np.random.uniform(0, 2 * np.pi)
        distance = np.random.uniform(10, 25)
        
        sun = CelestialBody(
            position=Vector3D(
                np.cos(angle) * distance,
                np.random.uniform(-5, 5),
                np.sin(angle) * distance
            ),
            velocity=Vector3D(
                np.random.uniform(-1, 1),
                np.random.uniform(-1, 1),
                np.random.uniform(-1, 1)
            ),
            mass=self.body_mass_var.get() * 5,
            radius=self.body_radius_var.get() * 2,
            color='orange',
            body_type='sun',
            name=f'Sun_{len([b for b in self.physics_system.bodies if b.body_type == "sun"]) + 1}'
        )
        
        self.physics_system.add_body(sun)
    
    def add_planet(self):
        """Add a new planet to the system"""
        # Find most massive body for orbital reference
        central_body = max(self.physics_system.bodies, key=lambda b: b.mass, default=None)
        
        if central_body:
            angle = np.random.uniform(0, 2 * np.pi)
            distance = np.random.uniform(8, 30)
            
            planet = CelestialBody(
                position=Vector3D(
                    central_body.position.x + np.cos(angle) * distance,
                    central_body.position.y + np.random.uniform(-3, 3),
                    central_body.position.z + np.sin(angle) * distance
                ),
                mass=self.body_mass_var.get(),
                radius=self.body_radius_var.get(),
                color=np.random.choice(['blue', 'red', 'green', 'purple', 'cyan']),
                body_type='planet',
                name=f'Planet_{len([b for b in self.physics_system.bodies if b.body_type == "planet"]) + 1}'
            )
            
            # Calculate orbital velocity for stable orbit
            if central_body.mass > 0:
                mu = PhysicsConstants.G * self.physics_system.gravitational_constant * central_body.mass
                orbital_speed = np.sqrt(mu / distance) * self.initial_velocity_var.get()
                
                # Perpendicular velocity for orbit
                planet.velocity = Vector3D(
                    central_body.velocity.x - np.sin(angle) * orbital_speed,
                    central_body.velocity.y + np.random.uniform(-0.1, 0.1) * orbital_speed,
                    central_body.velocity.z + np.cos(angle) * orbital_speed
                )
            
            self.physics_system.add_body(planet)
    
    def run(self):
        """Start the GUI main loop"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SpaceSimulationGUI()
    app.run()